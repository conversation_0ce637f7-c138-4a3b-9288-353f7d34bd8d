"""
Menu Strutture
Gestisce stadio, centro di allenamento e settore giovanile
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List
from .base_menu import BaseMenu
from ..gui_config import COLORS, FONTS, ICONS


class FacilitiesMenu(BaseMenu):
    """Menu per la gestione delle strutture"""
    
    def setup_ui(self):
        """Configura l'interfaccia del menu strutture"""
        # Header
        self.create_section_header(self.main_frame, "Gestione Strutture", 
                                 "Stadio, centro allenamento e settore giovanile")
        
        # Crea le schede per diverse strutture
        tabs_config = [
            {'text': 'Stadio', 'setup_func': self._setup_stadium_tab},
            {'text': 'Centro Allenamento', 'setup_func': self._setup_training_tab},
            {'text': 'Settore Giovanile', 'setup_func': self._setup_youth_tab},
            {'text': '<PERSON><PERSON><PERSON>', 'setup_func': self._setup_projects_tab}
        ]
        
        self.notebook = self.create_tabs(self.main_frame, tabs_config)
    
    def _setup_stadium_tab(self, parent: tk.Widget):
        """Configura la scheda stadio"""
        # Informazioni stadio
        stadium_info = ttk.LabelFrame(parent, text="Informazioni Stadio", padding=15)
        stadium_info.pack(fill='x', pady=(0, 10))
        
        # Grid per le informazioni
        info_grid = ttk.Frame(stadium_info)
        info_grid.pack(fill='x')
        
        # Colonna sinistra - Dati base
        left_col = ttk.Frame(info_grid)
        left_col.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_col, text="🏟️ Dati Generali", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        stadium_data = [
            ("Nome", self.club_data['stadio']),
            ("Capienza Totale", f"{self.club_data['capienza_stadio']:,}"),
            ("Condizione", f"{self.club_data['condizione_stadio']}/10 ({self.get_condition_text(self.club_data['condizione_stadio'])})"),
            ("Anno Costruzione", "1985"),
            ("Proprietà", "Club"),
            ("Superficie", "Erba naturale")
        ]
        
        for label, value in stadium_data:
            self.create_info_row(left_col, label, value)
        
        # Colonna destra - Settori
        right_col = ttk.Frame(info_grid)
        right_col.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_col, text="🎫 Settori", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        capacity = self.club_data['capienza_stadio']
        sectors_data = [
            ("Tribuna Principale", f"{int(capacity * 0.3):,}"),
            ("Tribuna Ospiti", f"{int(capacity * 0.15):,}"),
            ("Curva Nord", f"{int(capacity * 0.25):,}"),
            ("Curva Sud", f"{int(capacity * 0.25):,}"),
            ("Posti VIP", f"{int(capacity * 0.05):,}"),
            ("Media Presenze", f"{int(capacity * 0.78):,}")
        ]
        
        for label, value in sectors_data:
            self.create_info_row(right_col, label, value)
        
        # Ricavi stadio
        revenue_frame = ttk.LabelFrame(parent, text="Ricavi Stadio", padding=15)
        revenue_frame.pack(fill='x', pady=10)
        
        revenue_columns = [
            {'id': 'source', 'text': 'Fonte', 'width': 150},
            {'id': 'per_match', 'text': 'Per Partita', 'width': 100},
            {'id': 'season_total', 'text': 'Totale Stagione', 'width': 120},
            {'id': 'percentage', 'text': '% Ricavi', 'width': 80}
        ]
        
        revenue_data = [
            ["Biglietteria", "€180,000", "€3,600,000", "65%"],
            ["Abbonamenti", "€320,000", "€320,000", "6%"],
            ["Hospitality", "€45,000", "€900,000", "16%"],
            ["Merchandising", "€25,000", "€500,000", "9%"],
            ["Parcheggi", "€8,000", "€160,000", "3%"],
            ["Altri Servizi", "€12,000", "€240,000", "4%"]
        ]
        
        self.create_data_table(revenue_frame, revenue_columns, revenue_data, height=6)
        
        # Miglioramenti stadio
        improvements_frame = ttk.LabelFrame(parent, text="Miglioramenti Disponibili", padding=15)
        improvements_frame.pack(fill='both', expand=True)
        
        improvements_columns = [
            {'id': 'improvement', 'text': 'Miglioramento', 'width': 200},
            {'id': 'cost', 'text': 'Costo', 'width': 100},
            {'id': 'time', 'text': 'Tempo', 'width': 80},
            {'id': 'benefit', 'text': 'Beneficio', 'width': 150},
            {'id': 'status', 'text': 'Stato', 'width': 100}
        ]
        
        improvements_data = [
            ["Ampliamento Tribuna", "€8M", "8 mesi", "+5,000 posti", "Disponibile"],
            ["Rifacimento Manto", "€2M", "3 mesi", "Condizione +2", "Disponibile"],
            ["Nuovi Maxischermi", "€500K", "1 mese", "Ricavi +5%", "Disponibile"],
            ["Settore VIP", "€3M", "4 mesi", "Hospitality +50%", "In valutazione"],
            ["Copertura Curve", "€5M", "6 mesi", "Comfort +20%", "Pianificato"]
        ]
        
        improvements_tree = self.create_data_table(improvements_frame, improvements_columns, improvements_data, height=5)
        
        # Pulsanti stadio
        stadium_buttons = [
            {'text': '🔨 Avvia Progetto', 'command': self._start_stadium_project, 'style': 'Primary.TButton'},
            {'text': '📊 Analisi Ricavi', 'command': self._stadium_revenue_analysis, 'style': 'Secondary.TButton'},
            {'text': '📋 Report Struttura', 'command': self._stadium_report, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(improvements_frame, stadium_buttons)
    
    def _setup_training_tab(self, parent: tk.Widget):
        """Configura la scheda centro allenamento"""
        # Stato attuale
        current_frame = ttk.LabelFrame(parent, text="Centro Allenamento Attuale", padding=15)
        current_frame.pack(fill='x', pady=(0, 10))
        
        # Grid per le informazioni
        training_grid = ttk.Frame(current_frame)
        training_grid.pack(fill='x')
        
        # Colonna sinistra
        left_col = ttk.Frame(training_grid)
        left_col.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_col, text="🏃 Strutture Allenamento", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        training_level = self.club_data['strutture_allenamento']
        training_data = [
            ("Livello Generale", f"{training_level}/10 ({self.get_condition_text(training_level)})"),
            ("Campi Allenamento", "4 campi"),
            ("Palestra", "Completa"),
            ("Piscina", "Presente"),
            ("Centro Medico", "Attrezzato"),
            ("Foresteria", "20 posti")
        ]
        
        for label, value in training_data:
            self.create_info_row(left_col, label, value)
        
        # Colonna destra
        right_col = ttk.Frame(training_grid)
        right_col.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_col, text="📈 Benefici Attuali", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        benefits_data = [
            ("Sviluppo Giocatori", f"+{training_level * 2}%"),
            ("Recupero Infortuni", f"+{training_level * 3}%"),
            ("Condizione Fisica", f"+{training_level}%"),
            ("Morale Squadra", f"+{training_level}%"),
            ("Attrazione Talenti", f"+{training_level * 2}%"),
            ("Costi Manutenzione", f"€{training_level * 15000}/mese")
        ]
        
        for label, value in benefits_data:
            color = COLORS['success'] if '+' in str(value) else COLORS['warning'] if 'Costi' in label else None
            self.create_info_row(right_col, label, value, color)
        
        # Aree specifiche
        areas_frame = ttk.LabelFrame(parent, text="Aree Specifiche", padding=15)
        areas_frame.pack(fill='x', pady=10)
        
        areas_columns = [
            {'id': 'area', 'text': 'Area', 'width': 150},
            {'id': 'level', 'text': 'Livello', 'width': 80},
            {'id': 'condition', 'text': 'Condizione', 'width': 100},
            {'id': 'capacity', 'text': 'Capacità', 'width': 80},
            {'id': 'usage', 'text': 'Utilizzo', 'width': 80},
            {'id': 'efficiency', 'text': 'Efficienza', 'width': 80}
        ]
        
        areas_data = [
            ["Campo Principale", "9/10", "Eccellente", "25 giocatori", "95%", "Ottima"],
            ["Campo Secondario", "8/10", "Buona", "22 giocatori", "80%", "Buona"],
            ["Campo Sintetico", "7/10", "Buona", "20 giocatori", "70%", "Discreta"],
            ["Palestra Pesi", "8/10", "Buona", "15 giocatori", "85%", "Buona"],
            ["Piscina", "6/10", "Discreta", "8 giocatori", "60%", "Sufficiente"],
            ["Centro Medico", "9/10", "Eccellente", "5 giocatori", "90%", "Ottima"]
        ]
        
        self.create_data_table(areas_frame, areas_columns, areas_data, height=6)
        
        # Progetti miglioramento
        projects_frame = ttk.LabelFrame(parent, text="Progetti di Miglioramento", padding=15)
        projects_frame.pack(fill='both', expand=True)
        
        projects_columns = [
            {'id': 'project', 'text': 'Progetto', 'width': 180},
            {'id': 'cost', 'text': 'Costo', 'width': 100},
            {'id': 'time', 'text': 'Tempo', 'width': 80},
            {'id': 'improvement', 'text': 'Miglioramento', 'width': 120},
            {'id': 'priority', 'text': 'Priorità', 'width': 80}
        ]
        
        projects_data = [
            ["Nuovo Campo Sintetico", "€1.5M", "4 mesi", "Livello +1", "Alta"],
            ["Ampliamento Palestra", "€800K", "2 mesi", "Capacità +50%", "Media"],
            ["Ristrutturazione Piscina", "€600K", "3 mesi", "Livello +2", "Media"],
            ["Centro Riabilitazione", "€2M", "5 mesi", "Recupero +30%", "Alta"],
            ["Foresteria Ampliata", "€1.2M", "6 mesi", "+15 posti", "Bassa"]
        ]
        
        self.create_data_table(projects_frame, projects_columns, projects_data, height=5)
        
        # Pulsanti centro allenamento
        training_buttons = [
            {'text': '🚀 Avvia Progetto', 'command': self._start_training_project, 'style': 'Primary.TButton'},
            {'text': '📊 Analisi Efficienza', 'command': self._training_efficiency, 'style': 'Secondary.TButton'},
            {'text': '⚙️ Gestione Manutenzione', 'command': self._training_maintenance, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(projects_frame, training_buttons)
    
    def _setup_youth_tab(self, parent: tk.Widget):
        """Configura la scheda settore giovanile"""
        # Stato attuale settore giovanile
        youth_info = ttk.LabelFrame(parent, text="Settore Giovanile", padding=15)
        youth_info.pack(fill='x', pady=(0, 10))
        
        # Grid per le informazioni
        youth_grid = ttk.Frame(youth_info)
        youth_grid.pack(fill='x')
        
        # Colonna sinistra
        left_col = ttk.Frame(youth_grid)
        left_col.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_col, text="🌱 Strutture Giovanili", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        youth_level = self.club_data['strutture_giovanili']
        recruitment_level = self.club_data['reclutamento_giovanile']
        
        youth_data = [
            ("Livello Strutture", f"{youth_level}/10 ({self.get_condition_text(youth_level)})"),
            ("Livello Reclutamento", f"{recruitment_level}/10 ({self.get_condition_text(recruitment_level)})"),
            ("Campi Dedicati", "2 campi"),
            ("Alloggi", "15 posti"),
            ("Staff Giovanile", "8 membri"),
            ("Categorie Attive", "6 squadre")
        ]
        
        for label, value in youth_data:
            self.create_info_row(left_col, label, value)
        
        # Colonna destra
        right_col = ttk.Frame(youth_grid)
        right_col.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_col, text="📊 Statistiche", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        youth_stats = [
            ("Giovani Totali", "85"),
            ("Promossi in Prima Squadra", "3"),
            ("Venduti Proficuamente", "5"),
            ("Valore Generato", "€2.5M"),
            ("Costi Annuali", "€800K"),
            ("ROI", "+212%")
        ]
        
        for label, value in youth_stats:
            color = COLORS['success'] if any(x in str(value) for x in ['+', 'M']) else None
            self.create_info_row(right_col, label, value, color)
        
        # Squadre giovanili
        teams_frame = ttk.LabelFrame(parent, text="Squadre Giovanili", padding=15)
        teams_frame.pack(fill='x', pady=10)
        
        teams_columns = [
            {'id': 'category', 'text': 'Categoria', 'width': 100},
            {'id': 'players', 'text': 'Giocatori', 'width': 80},
            {'id': 'coach', 'text': 'Allenatore', 'width': 120},
            {'id': 'league_pos', 'text': 'Pos. Camp.', 'width': 80},
            {'id': 'talents', 'text': 'Talenti', 'width': 80},
            {'id': 'budget', 'text': 'Budget', 'width': 80}
        ]
        
        teams_data = [
            ["Primavera", "22", "G. Rossi", "3°", "5", "€150K"],
            ["Under 18", "20", "M. Bianchi", "1°", "3", "€100K"],
            ["Under 17", "18", "L. Verdi", "5°", "2", "€80K"],
            ["Under 16", "16", "A. Neri", "2°", "4", "€70K"],
            ["Under 15", "15", "F. Blu", "4°", "1", "€60K"],
            ["Under 14", "14", "C. Gialli", "1°", "2", "€50K"]
        ]
        
        self.create_data_table(teams_frame, teams_columns, teams_data, height=6)
        
        # Talenti promettenti
        talents_frame = ttk.LabelFrame(parent, text="Talenti Promettenti", padding=15)
        talents_frame.pack(fill='both', expand=True)
        
        talents_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'age', 'text': 'Età', 'width': 40},
            {'id': 'position', 'text': 'Pos', 'width': 40},
            {'id': 'category', 'text': 'Categoria', 'width': 80},
            {'id': 'potential', 'text': 'Potenziale', 'width': 80},
            {'id': 'current', 'text': 'Attuale', 'width': 60},
            {'id': 'interest', 'text': 'Interesse', 'width': 80},
            {'id': 'value', 'text': 'Valore', 'width': 80}
        ]
        
        talents_data = [
            ["Marco Giovani", "17", "ATT", "Under 18", "Molto Alto", "68", "Milan, Juve", "€500K"],
            ["Luca Promessa", "16", "CC", "Under 17", "Alto", "65", "Roma", "€300K"],
            ["Andrea Futuro", "18", "DIF", "Primavera", "Alto", "70", "Atalanta", "€400K"],
            ["Simone Talento", "15", "ATT", "Under 16", "Molto Alto", "62", "Inter", "€200K"],
            ["Giuseppe Stella", "17", "POR", "Under 18", "Medio", "66", "-", "€150K"]
        ]
        
        talents_tree = self.create_data_table(talents_frame, talents_columns, talents_data, height=5)
        
        # Pulsanti settore giovanile
        youth_buttons = [
            {'text': '⭐ Promuovi Talento', 'command': self._promote_talent, 'style': 'Primary.TButton'},
            {'text': '🔍 Scout Giovanili', 'command': self._youth_scouting, 'style': 'Secondary.TButton'},
            {'text': '📈 Migliora Strutture', 'command': self._improve_youth, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(talents_frame, youth_buttons)
    
    def _setup_projects_tab(self, parent: tk.Widget):
        """Configura la scheda progetti"""
        # Progetti in corso
        active_frame = ttk.LabelFrame(parent, text="Progetti in Corso", padding=15)
        active_frame.pack(fill='x', pady=(0, 10))
        
        active_columns = [
            {'id': 'project', 'text': 'Progetto', 'width': 200},
            {'id': 'area', 'text': 'Area', 'width': 120},
            {'id': 'progress', 'text': 'Progresso', 'width': 80},
            {'id': 'completion', 'text': 'Completamento', 'width': 100},
            {'id': 'budget_used', 'text': 'Budget Usato', 'width': 100},
            {'id': 'status', 'text': 'Stato', 'width': 100}
        ]
        
        active_data = [
            ["Rifacimento Manto Erboso", "Stadio", "75%", "15/02/2025", "€1.5M/€2M", "In corso"],
            ["Ampliamento Palestra", "Centro Allenamento", "40%", "30/03/2025", "€320K/€800K", "In corso"]
        ]
        
        self.create_data_table(active_frame, active_columns, active_data, height=4)
        
        # Progetti pianificati
        planned_frame = ttk.LabelFrame(parent, text="Progetti Pianificati", padding=15)
        planned_frame.pack(fill='x', pady=10)
        
        planned_columns = [
            {'id': 'project', 'text': 'Progetto', 'width': 200},
            {'id': 'area', 'text': 'Area', 'width': 120},
            {'id': 'cost', 'text': 'Costo Stimato', 'width': 100},
            {'id': 'duration', 'text': 'Durata', 'width': 80},
            {'id': 'start_date', 'text': 'Inizio Previsto', 'width': 100},
            {'id': 'priority', 'text': 'Priorità', 'width': 80}
        ]
        
        planned_data = [
            ["Nuovo Settore VIP", "Stadio", "€3M", "4 mesi", "Giugno 2025", "Alta"],
            ["Centro Riabilitazione", "Centro Allenamento", "€2M", "5 mesi", "Aprile 2025", "Alta"],
            ["Ampliamento Foresteria", "Settore Giovanile", "€1.2M", "6 mesi", "Agosto 2025", "Media"],
            ["Copertura Curve", "Stadio", "€5M", "8 mesi", "Settembre 2025", "Bassa"]
        ]
        
        self.create_data_table(planned_frame, planned_columns, planned_data, height=4)
        
        # Budget e finanziamenti
        budget_frame = ttk.LabelFrame(parent, text="Budget Strutture", padding=15)
        budget_frame.pack(fill='both', expand=True)
        
        # Grid per budget
        budget_grid = ttk.Frame(budget_frame)
        budget_grid.pack(fill='x')
        
        # Colonna sinistra - Budget disponibile
        budget_left = ttk.Frame(budget_grid)
        budget_left.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(budget_left, text="💰 Budget Disponibile", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        budget_data = [
            ("Budget Totale Strutture", "€15M"),
            ("Utilizzato", "€2.3M"),
            ("Rimanente", "€12.7M"),
            ("Progetti Approvati", "€8.2M"),
            ("Disponibile per Nuovi", "€4.5M"),
            ("Finanziamenti Esterni", "€3M")
        ]
        
        for label, value in budget_data:
            color = COLORS['success'] if 'Rimanente' in label or 'Disponibile' in label else None
            self.create_info_row(budget_left, label, value, color)
        
        # Colonna destra - ROI previsto
        budget_right = ttk.Frame(budget_grid)
        budget_right.pack(side='right', fill='both', expand=True)
        
        ttk.Label(budget_right, text="📈 ROI Previsto", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        roi_data = [
            ("Aumento Ricavi Stadio", "+€2M/anno"),
            ("Risparmio Manutenzione", "+€300K/anno"),
            ("Valore Giocatori", "+15%"),
            ("Attrattività Club", "+20%"),
            ("Payback Period", "4.2 anni"),
            ("ROI a 10 anni", "+180%")
        ]
        
        for label, value in roi_data:
            color = COLORS['success'] if '+' in str(value) else None
            self.create_info_row(budget_right, label, value, color)
        
        # Pulsanti progetti
        projects_buttons = [
            {'text': '🚀 Avvia Nuovo Progetto', 'command': self._start_new_project, 'style': 'Primary.TButton'},
            {'text': '⏸️ Pausa Progetto', 'command': self._pause_project, 'style': 'Secondary.TButton'},
            {'text': '📊 Report Avanzamento', 'command': self._project_report, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(budget_frame, projects_buttons)
    
    # Metodi placeholder per le azioni
    def _start_stadium_project(self):
        self.show_message("Avvia Progetto Stadio", "Funzionalità in sviluppo", "info")
    
    def _stadium_revenue_analysis(self):
        self.show_message("Analisi Ricavi Stadio", "Funzionalità in sviluppo", "info")
    
    def _stadium_report(self):
        self.show_message("Report Stadio", "Funzionalità in sviluppo", "info")
    
    def _start_training_project(self):
        self.show_message("Avvia Progetto Centro Allenamento", "Funzionalità in sviluppo", "info")
    
    def _training_efficiency(self):
        self.show_message("Analisi Efficienza", "Funzionalità in sviluppo", "info")
    
    def _training_maintenance(self):
        self.show_message("Gestione Manutenzione", "Funzionalità in sviluppo", "info")
    
    def _promote_talent(self):
        self.show_message("Promuovi Talento", "Funzionalità in sviluppo", "info")
    
    def _youth_scouting(self):
        self.show_message("Scout Giovanili", "Funzionalità in sviluppo", "info")
    
    def _improve_youth(self):
        self.show_message("Migliora Strutture Giovanili", "Funzionalità in sviluppo", "info")
    
    def _start_new_project(self):
        self.show_message("Avvia Nuovo Progetto", "Funzionalità in sviluppo", "info")
    
    def _pause_project(self):
        self.show_message("Pausa Progetto", "Funzionalità in sviluppo", "info")
    
    def _project_report(self):
        self.show_message("Report Avanzamento", "Funzionalità in sviluppo", "info")
