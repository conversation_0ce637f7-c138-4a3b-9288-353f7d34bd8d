"""
Menu Finanze
Gestisce budget, ricavi, spese e situazione finanziaria del club
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List
from .base_menu import BaseMenu
from ..gui_config import COLORS, FONTS, ICONS


class FinancesMenu(BaseMenu):
    """Menu per la gestione delle finanze"""
    
    def __init__(self, parent: tk.Widget, club_data: Dict, game_engine, data_loader):
        super().__init__(parent, club_data, game_engine, data_loader)
        
        # Dati finanziari (da sostituire con dati reali dal game engine)
        self.financial_data = self._generate_financial_data()
    
    def setup_ui(self):
        """Configura l'interfaccia del menu finanze"""
        # Header
        self.create_section_header(self.main_frame, "Gestione Finanze", 
                                 f"Situazione finanziaria del {self.club_data['nome']}")
        
        # <PERSON><PERSON> le schede per diverse sezioni finanziarie
        tabs_config = [
            {'text': 'Panoramica', 'setup_func': self._setup_overview_tab},
            {'text': 'Budget', 'setup_func': self._setup_budget_tab},
            {'text': 'Ricavi', 'setup_func': self._setup_revenue_tab},
            {'text': 'Spese', 'setup_func': self._setup_expenses_tab},
            {'text': 'Storico', 'setup_func': self._setup_history_tab}
        ]
        
        self.notebook = self.create_tabs(self.main_frame, tabs_config)
    
    def _setup_overview_tab(self, parent: tk.Widget):
        """Configura la scheda panoramica"""
        # Situazione finanziaria attuale
        current_frame = ttk.LabelFrame(parent, text="Situazione Attuale", padding=15)
        current_frame.pack(fill='x', pady=(0, 10))
        
        # Grid per organizzare le informazioni
        overview_grid = ttk.Frame(current_frame)
        overview_grid.pack(fill='x')
        
        # Colonna sinistra - Liquidità
        left_col = ttk.Frame(overview_grid)
        left_col.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_col, text="💰 Liquidità", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        liquidity_data = [
            ("Cassa Disponibile", self.format_currency(self.financial_data['cash_balance'])),
            ("Budget Trasferimenti", self.format_currency(self.club_data['budget_trasferimenti_eur'])),
            ("Budget Stipendi", self.format_currency(self.club_data['budget_stipendi_eur'])),
            ("Crediti", self.format_currency(self.financial_data['receivables']))
        ]
        
        for label, value in liquidity_data:
            color = COLORS['success'] if 'Disponibile' in label else None
            self.create_info_row(left_col, label, value, color)
        
        # Colonna destra - Performance
        right_col = ttk.Frame(overview_grid)
        right_col.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_col, text="📊 Performance", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        performance_data = [
            ("Ricavi Mensili", self.format_currency(self.financial_data['monthly_revenue'])),
            ("Spese Mensili", self.format_currency(self.financial_data['monthly_expenses'])),
            ("Profitto/Perdita", self.format_currency(self.financial_data['monthly_profit'])),
            ("Margine", self.format_percentage(self.financial_data['profit_margin']))
        ]
        
        for label, value in performance_data:
            color = None
            if 'Profitto' in label:
                color = COLORS['success'] if self.financial_data['monthly_profit'] > 0 else COLORS['error']
            self.create_info_row(right_col, label, value, color)
        
        # Indicatori finanziari
        indicators_frame = ttk.LabelFrame(parent, text="Indicatori Finanziari", padding=15)
        indicators_frame.pack(fill='x', pady=10)
        
        # Barre di progresso per vari indicatori
        indicators = [
            ("Stabilità Finanziaria", 75, 100),
            ("Sostenibilità Budget", 68, 100),
            ("Liquidità", 82, 100),
            ("Efficienza Costi", 71, 100)
        ]
        
        for label, value, max_val in indicators:
            self.create_progress_bar(indicators_frame, label, value, max_val)
        
        # Avvisi e raccomandazioni
        alerts_frame = ttk.LabelFrame(parent, text="Avvisi e Raccomandazioni", padding=15)
        alerts_frame.pack(fill='both', expand=True)
        
        alerts = [
            ("⚠️", "Il budget stipendi è al 85% della capacità", COLORS['warning']),
            ("ℹ️", "Ricavi da merchandising in crescita del 12%", COLORS['info']),
            ("✅", "Situazione finanziaria stabile", COLORS['success'])
        ]
        
        for icon, message, color in alerts:
            alert_frame = ttk.Frame(alerts_frame)
            alert_frame.pack(fill='x', pady=2)
            
            ttk.Label(alert_frame, text=icon, font=FONTS['normal']).pack(side='left', padx=(0, 10))
            ttk.Label(alert_frame, text=message, font=FONTS['normal'], 
                     foreground=color).pack(side='left')
    
    def _setup_budget_tab(self, parent: tk.Widget):
        """Configura la scheda budget"""
        # Budget corrente
        current_budget_frame = ttk.LabelFrame(parent, text="Budget Stagione 2025/2026", padding=15)
        current_budget_frame.pack(fill='x', pady=(0, 10))
        
        # Tabella budget
        budget_columns = [
            {'id': 'category', 'text': 'Categoria', 'width': 200},
            {'id': 'allocated', 'text': 'Stanziato', 'width': 120},
            {'id': 'used', 'text': 'Utilizzato', 'width': 120},
            {'id': 'remaining', 'text': 'Rimanente', 'width': 120},
            {'id': 'percentage', 'text': '%', 'width': 80}
        ]
        
        budget_data = [
            ["Trasferimenti in Entrata", 
             self.format_currency(self.club_data['budget_trasferimenti_eur']),
             self.format_currency(int(self.club_data['budget_trasferimenti_eur'] * 0.3)),
             self.format_currency(int(self.club_data['budget_trasferimenti_eur'] * 0.7)),
             "30%"],
            ["Stipendi Giocatori",
             self.format_currency(self.club_data['budget_stipendi_eur']),
             self.format_currency(int(self.club_data['budget_stipendi_eur'] * 0.85)),
             self.format_currency(int(self.club_data['budget_stipendi_eur'] * 0.15)),
             "85%"],
            ["Stipendi Staff",
             self.format_currency(500000),
             self.format_currency(420000),
             self.format_currency(80000),
             "84%"],
            ["Strutture e Manutenzione",
             self.format_currency(2000000),
             self.format_currency(800000),
             self.format_currency(1200000),
             "40%"],
            ["Marketing e Comunicazione",
             self.format_currency(1500000),
             self.format_currency(600000),
             self.format_currency(900000),
             "40%"]
        ]
        
        self.create_data_table(current_budget_frame, budget_columns, budget_data, height=6)
        
        # Controlli budget
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill='x', pady=10)
        
        buttons = [
            {'text': '📊 Analizza Budget', 'command': self._analyze_budget, 'style': 'Primary.TButton'},
            {'text': '⚙️ Modifica Allocazioni', 'command': self._modify_budget, 'style': 'Secondary.TButton'},
            {'text': '📋 Esporta Report', 'command': self._export_budget, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(controls_frame, buttons)
    
    def _setup_revenue_tab(self, parent: tk.Widget):
        """Configura la scheda ricavi"""
        # Ricavi per categoria
        revenue_frame = ttk.LabelFrame(parent, text="Ricavi per Categoria", padding=15)
        revenue_frame.pack(fill='x', pady=(0, 10))
        
        revenue_columns = [
            {'id': 'source', 'text': 'Fonte di Ricavo', 'width': 200},
            {'id': 'monthly', 'text': 'Mensile', 'width': 120},
            {'id': 'yearly', 'text': 'Annuale (Stima)', 'width': 120},
            {'id': 'growth', 'text': 'Crescita', 'width': 100}
        ]
        
        revenue_data = [
            ["Biglietteria", "€180K", "€2.16M", "+5.2%"],
            ["Abbonamenti", "€320K", "€3.84M", "+8.1%"],
            ["Sponsorizzazioni", "€450K", "€5.4M", "+12.3%"],
            ["Merchandising", "€85K", "€1.02M", "+15.7%"],
            ["Diritti TV", "€280K", "€3.36M", "+3.4%"],
            ["Premi UEFA", "€120K", "€1.44M", "+22.1%"],
            ["Cessioni Giocatori", "€200K", "€2.4M", "-8.9%"],
            ["Altri Ricavi", "€65K", "€780K", "+6.8%"]
        ]
        
        self.create_data_table(revenue_frame, revenue_columns, revenue_data, height=8)
        
        # Grafico ricavi (placeholder)
        chart_frame = ttk.LabelFrame(parent, text="Andamento Ricavi", padding=15)
        chart_frame.pack(fill='both', expand=True)
        
        # Canvas per grafico semplificato
        canvas = tk.Canvas(chart_frame, bg='white', height=200)
        canvas.pack(fill='both', expand=True, pady=10)
        
        # Disegna un grafico a barre semplificato
        canvas.create_text(200, 100, text="📈 Grafico Ricavi\n(In sviluppo)", 
                          font=FONTS['subtitle'], fill=COLORS['text_secondary'])
    
    def _setup_expenses_tab(self, parent: tk.Widget):
        """Configura la scheda spese"""
        # Spese per categoria
        expenses_frame = ttk.LabelFrame(parent, text="Spese per Categoria", padding=15)
        expenses_frame.pack(fill='x', pady=(0, 10))
        
        expenses_columns = [
            {'id': 'category', 'text': 'Categoria di Spesa', 'width': 200},
            {'id': 'monthly', 'text': 'Mensile', 'width': 120},
            {'id': 'yearly', 'text': 'Annuale (Stima)', 'width': 120},
            {'id': 'budget_pct', 'text': '% Budget', 'width': 100}
        ]
        
        expenses_data = [
            ["Stipendi Giocatori", "€375K", "€4.5M", "65.2%"],
            ["Stipendi Staff", "€42K", "€504K", "7.3%"],
            ["Manutenzione Stadio", "€35K", "€420K", "6.1%"],
            ["Trasferte e Logistica", "€28K", "€336K", "4.9%"],
            ["Utilities e Servizi", "€22K", "€264K", "3.8%"],
            ["Marketing", "€18K", "€216K", "3.1%"],
            ["Assicurazioni", "€15K", "€180K", "2.6%"],
            ["Spese Legali/Admin", "€12K", "€144K", "2.1%"],
            ["Altre Spese", "€25K", "€300K", "4.3%"]
        ]
        
        self.create_data_table(expenses_frame, expenses_columns, expenses_data, height=9)
        
        # Analisi costi
        analysis_frame = ttk.LabelFrame(parent, text="Analisi Costi", padding=15)
        analysis_frame.pack(fill='both', expand=True)
        
        # Metriche di efficienza
        efficiency_grid = ttk.Frame(analysis_frame)
        efficiency_grid.pack(fill='x')
        
        left_metrics = ttk.Frame(efficiency_grid)
        left_metrics.pack(side='left', fill='both', expand=True)
        
        right_metrics = ttk.Frame(efficiency_grid)
        right_metrics.pack(side='right', fill='both', expand=True)
        
        left_data = [
            ("Costo per Punto", "€45,200"),
            ("Costo per Gol", "€89,500"),
            ("Efficienza Stipendi", "72%")
        ]
        
        right_data = [
            ("Costo per Vittoria", "€156,800"),
            ("ROI Trasferimenti", "125%"),
            ("Indice Sostenibilità", "68%")
        ]
        
        for label, value in left_data:
            self.create_info_row(left_metrics, label, value)
        
        for label, value in right_data:
            self.create_info_row(right_metrics, label, value)
    
    def _setup_history_tab(self, parent: tk.Widget):
        """Configura la scheda storico"""
        # Storico finanziario
        history_frame = ttk.LabelFrame(parent, text="Storico Finanziario (Ultime 5 Stagioni)", padding=15)
        history_frame.pack(fill='x', pady=(0, 10))
        
        history_columns = [
            {'id': 'season', 'text': 'Stagione', 'width': 100},
            {'id': 'revenue', 'text': 'Ricavi', 'width': 120},
            {'id': 'expenses', 'text': 'Spese', 'width': 120},
            {'id': 'profit', 'text': 'Profitto/Perdita', 'width': 120},
            {'id': 'position', 'text': 'Posizione', 'width': 80}
        ]
        
        history_data = [
            ["2024/25", "€16.2M", "€14.8M", "€1.4M", "3°"],
            ["2023/24", "€15.1M", "€15.9M", "-€0.8M", "7°"],
            ["2022/23", "€14.5M", "€13.2M", "€1.3M", "4°"],
            ["2021/22", "€13.8M", "€14.1M", "-€0.3M", "9°"],
            ["2020/21", "€12.9M", "€12.1M", "€0.8M", "5°"]
        ]
        
        self.create_data_table(history_frame, history_columns, history_data, height=5)
        
        # Trend analysis
        trends_frame = ttk.LabelFrame(parent, text="Analisi Trend", padding=15)
        trends_frame.pack(fill='both', expand=True)
        
        trends_data = [
            ("Crescita Ricavi (5 anni)", "+25.6%", COLORS['success']),
            ("Controllo Costi", "+22.3%", COLORS['warning']),
            ("Profittabilità Media", "€0.48M/anno", COLORS['success']),
            ("Volatilità Finanziaria", "Moderata", COLORS['info'])
        ]
        
        for label, value, color in trends_data:
            self.create_info_row(trends_frame, label, value, color)
    
    def _generate_financial_data(self) -> Dict:
        """Genera dati finanziari di esempio"""
        return {
            'cash_balance': 8500000,
            'receivables': 2300000,
            'monthly_revenue': 1500000,
            'monthly_expenses': 1200000,
            'monthly_profit': 300000,
            'profit_margin': 20.0
        }
    
    def _analyze_budget(self):
        """Analizza il budget corrente"""
        self.show_message("Analisi Budget", "Funzionalità di analisi budget in sviluppo", "info")
    
    def _modify_budget(self):
        """Modifica le allocazioni di budget"""
        self.show_message("Modifica Budget", "Funzionalità di modifica budget in sviluppo", "info")
    
    def _export_budget(self):
        """Esporta il report budget"""
        self.show_message("Esporta Report", "Funzionalità di esportazione in sviluppo", "info")
