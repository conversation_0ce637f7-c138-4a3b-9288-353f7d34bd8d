"""
Test script to verify the new modules work correctly
"""

import sys
from pathlib import Path

# Add src directory to path for imports
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Add src to Python path for relative imports
sys.path.insert(0, str(Path(__file__).parent))

from game.game_engine import GameEngine
from data.data_loader import DataLoader
from data.name_generator import NameGenerator
from club.club import Club
from staff.coaching_staff import Coach
from staff.staff_generator import StaffGenerator
from ai.coach_ai import Coach<PERSON><PERSON>
from competitions.league_manager import League
from competitions.match_engine import MatchEngine

def test_coach_creation():
    """Test creating coaches"""
    print("=== Testing Coach Creation ===")
    
    # Create a coach
    coach = Coach("<PERSON>", "Mourinh<PERSON>", "Portogallo", 60)
    print(f"Created coach: {coach}")
    print(f"Coaching rating: {coach.calculate_coaching_rating()}")
    
    # Add a trophy
    coach.add_trophy("Premier League", 2024)
    print(f"Trophies won: {coach.get_trophy_count()}")
    
    print()

def test_staff_generator():
    """Test staff generator"""
    print("=== Testing Staff Generator ===")
    
    # Initialize data loader and name generator
    data_loader = DataLoader()
    data_loader.load_all_data()
    name_gen = NameGenerator(data_loader)
    
    # Create staff generator
    staff_gen = StaffGenerator(data_loader, name_gen)
    
    # Generate a coach
    coach = staff_gen.generate_coach("Italia", "high")
    print(f"Generated coach: {coach}")
    print(f"Coaching rating: {coach.calculate_coaching_rating()}")
    
    # Generate staff team
    staff_team = staff_gen.generate_staff_team(18)  # High reputation club
    print(f"Generated {len(staff_team)} staff members:")
    for staff in staff_team:
        print(f"  - {staff} (Rating: {staff.calculate_coaching_rating()})")
    
    print()

def test_coach_ai():
    """Test coach AI"""
    print("=== Testing Coach AI ===")
    
    # Create a coach
    coach = Coach("Pep", "Guardiola", "Spagna", 53)
    coach.tactical_knowledge = 18
    coach.preferred_formation = coach.preferred_formation.FOUR_THREE_THREE
    
    # Create AI
    ai = CoachAI(coach)
    print(f"Coach AI created for {coach.full_name}")
    print(f"Tactical preference: {ai.tactical_preference}")
    print(f"Preferred formations: {[f.value for f in ai.preferred_formations]}")
    
    print()

def test_league_manager():
    """Test league manager"""
    print("=== Testing League Manager ===")
    
    # Load some clubs
    data_loader = DataLoader()
    data_loader.load_all_data()
    clubs_data = data_loader.get_clubs_by_country("Italia")[:6]  # First 6 clubs
    
    # Create club objects
    clubs = [Club(club_data) for club_data in clubs_data]
    
    # Create league data (simplified)
    league_data = {
        "nome_campionato": "Test League",
        "paese": "Italia",
        "livello_competizione": 1,
        "stagione": "2025/2026"
    }
    
    # Create league
    league = League(league_data, clubs)
    print(f"Created league: {league}")
    print(f"Number of clubs: {len(league.clubs)}")
    
    # Generate fixtures
    fixtures = league.generate_fixtures()
    print(f"Generated {len(fixtures)} fixtures")
    print(f"Total matchdays: {league.total_matchdays}")
    
    print()

def test_match_engine():
    """Test match engine"""
    print("=== Testing Match Engine ===")
    
    # Create match engine
    engine = MatchEngine()
    print("Match engine created")
    
    # Load some clubs for testing
    data_loader = DataLoader()
    data_loader.load_all_data()
    clubs_data = data_loader.get_clubs_by_country("Italia")[:2]  # First 2 clubs
    
    # Create club objects
    home_club = Club(clubs_data[0])
    away_club = Club(clubs_data[1])
    
    # Simulate a match
    result = engine.simulate_match(home_club, away_club)
    print(f"Match simulated: {engine.get_match_result_string(result)}")
    print(f"Home possession: {result.home_possession}%")
    print(f"Away possession: {result.away_possession}%")
    print(f"Home shots: {result.home_shots} ({result.home_shots_on_target} on target)")
    print(f"Away shots: {result.away_shots} ({result.away_shots_on_target} on target)")
    print(f"Match events: {len(result.events)}")
    
    for event in result.events[:3]:  # Show first 3 events
        print(f"  - {event.minute}': {event.description}")
    
    print()

def main():
    """Run all tests"""
    print("Football Manager - Module Tests")
    print("=" * 40)
    
    try:
        test_coach_creation()
        test_staff_generator()
        test_coach_ai()
        test_league_manager()
        test_match_engine()
        
        print("All tests completed successfully!")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()