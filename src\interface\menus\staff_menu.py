"""
Menu Staff
Gestisce allenatori, preparatori e tutto lo staff tecnico
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List
from .base_menu import BaseMenu
from ..gui_config import COLORS, FONTS, ICONS


class StaffMenu(BaseMenu):
    """Menu per la gestione dello staff"""
    
    def setup_ui(self):
        """Configura l'interfaccia del menu staff"""
        # Header
        self.create_section_header(self.main_frame, "Gestione Staff", 
                                 "Allenatori, preparatori e staff tecnico")
        
        # Crea le schede per diverse categorie di staff
        tabs_config = [
            {'text': 'Staff Tecnico', 'setup_func': self._setup_technical_tab},
            {'text': 'Staff Medico', 'setup_func': self._setup_medical_tab},
            {'text': 'Scouting', 'setup_func': self._setup_scouting_tab},
            {'text': 'Ricerca', 'setup_func': self._setup_search_tab}
        ]
        
        self.notebook = self.create_tabs(self.main_frame, tabs_config)
    
    def _setup_technical_tab(self, parent: tk.Widget):
        """Configura la scheda staff tecnico"""
        # Staff tecnico attuale
        current_frame = ttk.LabelFrame(parent, text="Staff Tecnico Attuale", padding=15)
        current_frame.pack(fill='x', pady=(0, 10))
        
        technical_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'role', 'text': 'Ruolo', 'width': 120},
            {'id': 'age', 'text': 'Età', 'width': 40},
            {'id': 'nationality', 'text': 'Nazionalità', 'width': 80},
            {'id': 'rating', 'text': 'Valutazione', 'width': 80},
            {'id': 'contract', 'text': 'Contratto', 'width': 80},
            {'id': 'salary', 'text': 'Stipendio', 'width': 80}
        ]
        
        technical_data = [
            ["Roberto Mancini", "Allenatore Principale", "58", "Italia", "85/100", "2026", "€2M/anno"],
            ["Gianluca Vialli", "Vice Allenatore", "55", "Italia", "78/100", "2025", "€800K/anno"],
            ["Antonio Conte", "Preparatore Atletico", "52", "Italia", "82/100", "2025", "€600K/anno"],
            ["Gianluigi Buffon", "Allenatore Portieri", "45", "Italia", "88/100", "2027", "€500K/anno"],
            ["Daniele De Rossi", "Allenatore Tattico", "40", "Italia", "75/100", "2024", "€400K/anno"]
        ]
        
        technical_tree = self.create_data_table(current_frame, technical_columns, technical_data, height=5)
        
        # Pulsanti staff tecnico
        technical_buttons = [
            {'text': '👁️ Dettagli', 'command': self._staff_details, 'style': 'Primary.TButton'},
            {'text': '💰 Rinnova Contratto', 'command': self._renew_contract, 'style': 'Secondary.TButton'},
            {'text': '🔄 Sostituisci', 'command': self._replace_staff, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(current_frame, technical_buttons)
        
        # Statistiche staff
        stats_frame = ttk.LabelFrame(parent, text="Statistiche Staff", padding=15)
        stats_frame.pack(fill='x', pady=10)
        
        # Grid per le statistiche
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill='x')
        
        # Colonna sinistra
        left_stats = ttk.Frame(stats_grid)
        left_stats.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_stats, text="📊 Prestazioni", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        performance_stats = [
            ("Vittorie", "12/20 (60%)"),
            ("Punti per Partita", "2.05"),
            ("Gol Fatti/Partita", "1.4"),
            ("Gol Subiti/Partita", "0.75"),
            ("Possesso Medio", "58.2%"),
            ("Soddisfazione Squadra", "85%")
        ]
        
        for label, value in performance_stats:
            color = COLORS['success'] if any(x in str(value) for x in ['60%', '2.05', '85%']) else None
            self.create_info_row(left_stats, label, value, color)
        
        # Colonna destra
        right_stats = ttk.Frame(stats_grid)
        right_stats.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_stats, text="💼 Gestione", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        management_stats = [
            ("Costo Totale Staff", "€4.3M/anno"),
            ("Staff Membri", "15"),
            ("Contratti in Scadenza", "2"),
            ("Valutazione Media", "81/100"),
            ("Esperienza Media", "12.3 anni"),
            ("Stabilità Staff", "Alta")
        ]
        
        for label, value in management_stats:
            color = COLORS['warning'] if 'Scadenza' in label else COLORS['success'] if 'Alta' in str(value) else None
            self.create_info_row(right_stats, label, value, color)
        
        # Sviluppo staff
        development_frame = ttk.LabelFrame(parent, text="Sviluppo e Formazione", padding=15)
        development_frame.pack(fill='both', expand=True)
        
        development_columns = [
            {'id': 'staff', 'text': 'Staff', 'width': 120},
            {'id': 'course', 'text': 'Corso/Formazione', 'width': 150},
            {'id': 'progress', 'text': 'Progresso', 'width': 80},
            {'id': 'completion', 'text': 'Completamento', 'width': 100},
            {'id': 'cost', 'text': 'Costo', 'width': 80},
            {'id': 'benefit', 'text': 'Beneficio', 'width': 100}
        ]
        
        development_data = [
            ["R. Mancini", "Corso UEFA Pro", "80%", "Marzo 2025", "€5K", "Tattica +5"],
            ["G. Vialli", "Psicologia Sportiva", "60%", "Aprile 2025", "€3K", "Motivazione +3"],
            ["A. Conte", "Preparazione Atletica", "90%", "Febbraio 2025", "€4K", "Fitness +4"],
            ["G. Buffon", "Tecnica Portieri", "40%", "Maggio 2025", "€2K", "Portieri +2"]
        ]
        
        self.create_data_table(development_frame, development_columns, development_data, height=4)
        
        # Pulsanti sviluppo
        development_buttons = [
            {'text': '📚 Iscriviti Corso', 'command': self._enroll_course, 'style': 'Primary.TButton'},
            {'text': '📈 Piano Sviluppo', 'command': self._development_plan, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(development_frame, development_buttons)
    
    def _setup_medical_tab(self, parent: tk.Widget):
        """Configura la scheda staff medico"""
        # Staff medico attuale
        medical_frame = ttk.LabelFrame(parent, text="Staff Medico", padding=15)
        medical_frame.pack(fill='x', pady=(0, 10))
        
        medical_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'role', 'text': 'Specializzazione', 'width': 120},
            {'id': 'experience', 'text': 'Esperienza', 'width': 80},
            {'id': 'rating', 'text': 'Valutazione', 'width': 80},
            {'id': 'workload', 'text': 'Carico Lavoro', 'width': 80},
            {'id': 'salary', 'text': 'Stipendio', 'width': 80}
        ]
        
        medical_data = [
            ["Dr. Mario Rossi", "Medico Sportivo", "15 anni", "90/100", "Alto", "€150K/anno"],
            ["Dr. Anna Bianchi", "Fisioterapista", "8 anni", "85/100", "Medio", "€80K/anno"],
            ["Dr. Luca Verdi", "Ortopedico", "12 anni", "88/100", "Basso", "€120K/anno"],
            ["Marco Neri", "Massaggiatore", "6 anni", "75/100", "Alto", "€45K/anno"],
            ["Sara Blu", "Nutrizionista", "4 anni", "80/100", "Medio", "€60K/anno"]
        ]
        
        self.create_data_table(medical_frame, medical_columns, medical_data, height=5)
        
        # Statistiche mediche
        medical_stats_frame = ttk.LabelFrame(parent, text="Statistiche Mediche", padding=15)
        medical_stats_frame.pack(fill='x', pady=10)
        
        # Grid per statistiche mediche
        medical_grid = ttk.Frame(medical_stats_frame)
        medical_grid.pack(fill='x')
        
        # Colonna sinistra - Infortuni
        injuries_col = ttk.Frame(medical_grid)
        injuries_col.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(injuries_col, text="🏥 Infortuni", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        injury_stats = [
            ("Infortuni Totali", "8"),
            ("Giorni Persi", "156"),
            ("Tempo Medio Recupero", "19.5 giorni"),
            ("Ricadute", "1"),
            ("Prevenzione Efficacia", "78%"),
            ("Giocatori Disponibili", "92%")
        ]
        
        for label, value in injury_stats:
            color = COLORS['success'] if any(x in label for x in ['Disponibili', 'Efficacia']) else COLORS['warning'] if 'Ricadute' in label else None
            self.create_info_row(injuries_col, label, value, color)
        
        # Colonna destra - Fitness
        fitness_col = ttk.Frame(medical_grid)
        fitness_col.pack(side='right', fill='both', expand=True)
        
        ttk.Label(fitness_col, text="💪 Fitness", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        fitness_stats = [
            ("Condizione Media", "87%"),
            ("Affaticamento", "Basso"),
            ("Sessioni Riabilitative", "24"),
            ("Test Medici", "Tutti OK"),
            ("Programmi Personalizzati", "12"),
            ("Soddisfazione Giocatori", "91%")
        ]
        
        for label, value in fitness_stats:
            color = COLORS['success'] if any(x in str(value) for x in ['87%', '91%', 'OK', 'Basso']) else None
            self.create_info_row(fitness_col, label, value, color)
        
        # Infortuni attuali
        current_injuries_frame = ttk.LabelFrame(parent, text="Infortuni Attuali", padding=15)
        current_injuries_frame.pack(fill='both', expand=True)
        
        injuries_columns = [
            {'id': 'player', 'text': 'Giocatore', 'width': 120},
            {'id': 'injury', 'text': 'Infortunio', 'width': 150},
            {'id': 'date', 'text': 'Data', 'width': 80},
            {'id': 'severity', 'text': 'Gravità', 'width': 80},
            {'id': 'recovery', 'text': 'Recupero Stimato', 'width': 100},
            {'id': 'treatment', 'text': 'Trattamento', 'width': 120}
        ]
        
        injuries_data = [
            ["Marco Rossi", "Stiramento Quadricipite", "10/01", "Lieve", "5-7 giorni", "Fisioterapia"],
            ["Luca Bianchi", "Contusione Caviglia", "08/01", "Lieve", "3-4 giorni", "Riposo + Ghiaccio"],
            ["Giuseppe Verdi", "Affaticamento Muscolare", "12/01", "Minima", "2-3 giorni", "Massaggi"]
        ]
        
        self.create_data_table(current_injuries_frame, injuries_columns, injuries_data, height=4)
        
        # Pulsanti staff medico
        medical_buttons = [
            {'text': '🏥 Nuovo Trattamento', 'command': self._new_treatment, 'style': 'Primary.TButton'},
            {'text': '📊 Report Medico', 'command': self._medical_report, 'style': 'Secondary.TButton'},
            {'text': '💊 Gestione Farmaci', 'command': self._manage_medications, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(current_injuries_frame, medical_buttons)
    
    def _setup_scouting_tab(self, parent: tk.Widget):
        """Configura la scheda scouting"""
        # Scout attuali
        scouts_frame = ttk.LabelFrame(parent, text="Team di Scouting", padding=15)
        scouts_frame.pack(fill='x', pady=(0, 10))
        
        scouts_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'region', 'text': 'Regione', 'width': 100},
            {'id': 'specialty', 'text': 'Specializzazione', 'width': 100},
            {'id': 'rating', 'text': 'Valutazione', 'width': 80},
            {'id': 'reports', 'text': 'Report/Mese', 'width': 80},
            {'id': 'salary', 'text': 'Stipendio', 'width': 80}
        ]
        
        scouts_data = [
            ["Franco Baresi", "Italia Nord", "Difensori", "92/100", "15", "€80K/anno"],
            ["Paolo Maldini", "Europa", "Giovani Talenti", "95/100", "12", "€100K/anno"],
            ["Fabio Capello", "Sud America", "Centrocampisti", "88/100", "18", "€90K/anno"],
            ["Arrigo Sacchi", "Africa", "Attaccanti", "85/100", "10", "€70K/anno"],
            ["Gianni Rivera", "Italia Centro-Sud", "Portieri", "80/100", "8", "€60K/anno"]
        ]
        
        self.create_data_table(scouts_frame, scouts_columns, scouts_data, height=5)
        
        # Report scouting recenti
        reports_frame = ttk.LabelFrame(parent, text="Report Recenti", padding=15)
        reports_frame.pack(fill='x', pady=10)
        
        reports_columns = [
            {'id': 'date', 'text': 'Data', 'width': 80},
            {'id': 'scout', 'text': 'Scout', 'width': 100},
            {'id': 'player', 'text': 'Giocatore', 'width': 120},
            {'id': 'club', 'text': 'Club', 'width': 100},
            {'id': 'rating', 'text': 'Valutazione', 'width': 80},
            {'id': 'recommendation', 'text': 'Raccomandazione', 'width': 100}
        ]
        
        reports_data = [
            ["15/01", "P. Maldini", "Carlos Silva", "Santos", "85/100", "Acquisto"],
            ["14/01", "F. Baresi", "Marco Verratti", "PSG", "88/100", "Monitoraggio"],
            ["13/01", "F. Capello", "Gabriel Jesus", "Arsenal", "82/100", "Interesse"],
            ["12/01", "A. Sacchi", "Victor Osimhen", "Napoli", "90/100", "Priorità Alta"],
            ["11/01", "G. Rivera", "Gianluigi Donnarumma", "PSG", "92/100", "Impossibile"]
        ]
        
        reports_tree = self.create_data_table(reports_frame, reports_columns, reports_data, height=5)
        
        # Pulsanti scouting
        scouting_buttons = [
            {'text': '👁️ Dettagli Report', 'command': self._report_details, 'style': 'Primary.TButton'},
            {'text': '🎯 Nuova Missione', 'command': self._new_mission, 'style': 'Secondary.TButton'},
            {'text': '📋 Assegna Scout', 'command': self._assign_scout, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(reports_frame, scouting_buttons)
        
        # Statistiche scouting
        scouting_stats_frame = ttk.LabelFrame(parent, text="Statistiche Scouting", padding=15)
        scouting_stats_frame.pack(fill='both', expand=True)
        
        # Grid per statistiche scouting
        scouting_grid = ttk.Frame(scouting_stats_frame)
        scouting_grid.pack(fill='x')
        
        # Colonna sinistra
        left_scouting = ttk.Frame(scouting_grid)
        left_scouting.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_scouting, text="📊 Attività", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        activity_stats = [
            ("Report Totali", "156"),
            ("Giocatori Osservati", "89"),
            ("Raccomandazioni Acquisto", "23"),
            ("Successi Scouting", "78%"),
            ("Copertura Geografica", "15 paesi"),
            ("Budget Scouting", "€500K/anno")
        ]
        
        for label, value in activity_stats:
            self.create_info_row(left_scouting, label, value)
        
        # Colonna destra
        right_scouting = ttk.Frame(scouting_grid)
        right_scouting.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_scouting, text="🎯 Risultati", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        results_stats = [
            ("Acquisti da Scouting", "8"),
            ("Valore Totale Acquisti", "€45M"),
            ("ROI Scouting", "+340%"),
            ("Talenti Scoperti", "12"),
            ("Precisione Valutazioni", "85%"),
            ("Tempo Medio Report", "3.2 giorni")
        ]
        
        for label, value in results_stats:
            color = COLORS['success'] if any(x in str(value) for x in ['+', '%', 'M']) else None
            self.create_info_row(right_scouting, label, value, color)
    
    def _setup_search_tab(self, parent: tk.Widget):
        """Configura la scheda ricerca staff"""
        # Filtri ricerca
        search_frame = ttk.LabelFrame(parent, text="Ricerca Nuovo Staff", padding=15)
        search_frame.pack(fill='x', pady=(0, 10))
        
        # Filtri
        filters_row = ttk.Frame(search_frame)
        filters_row.pack(fill='x', pady=(0, 10))
        
        ttk.Label(filters_row, text="Ruolo:").pack(side='left', padx=(0, 5))
        role_combo = ttk.Combobox(filters_row, values=['Tutti', 'Allenatore', 'Vice Allenatore', 'Preparatore', 'Scout', 'Medico'], 
                                state='readonly', width=15)
        role_combo.set('Tutti')
        role_combo.pack(side='left', padx=(0, 20))
        
        ttk.Label(filters_row, text="Esperienza:").pack(side='left', padx=(0, 5))
        exp_combo = ttk.Combobox(filters_row, values=['Tutte', 'Junior (0-3)', 'Senior (4-10)', 'Esperto (10+)'], 
                               state='readonly', width=15)
        exp_combo.set('Tutte')
        exp_combo.pack(side='left', padx=(0, 20))
        
        ttk.Label(filters_row, text="Budget Max:").pack(side='left', padx=(0, 5))
        budget_entry = ttk.Entry(filters_row, width=12)
        budget_entry.insert(0, "€500K")
        budget_entry.pack(side='left')
        
        # Pulsante ricerca
        search_btn = ttk.Button(filters_row, text="🔍 Cerca", style='Primary.TButton')
        search_btn.pack(side='right')
        
        # Risultati ricerca
        results_frame = ttk.LabelFrame(parent, text="Candidati Disponibili", padding=10)
        results_frame.pack(fill='both', expand=True)
        
        candidates_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'role', 'text': 'Ruolo', 'width': 100},
            {'id': 'age', 'text': 'Età', 'width': 40},
            {'id': 'nationality', 'text': 'Nazionalità', 'width': 80},
            {'id': 'experience', 'text': 'Esperienza', 'width': 80},
            {'id': 'rating', 'text': 'Valutazione', 'width': 80},
            {'id': 'salary_request', 'text': 'Richiesta', 'width': 80},
            {'id': 'availability', 'text': 'Disponibilità', 'width': 100}
        ]
        
        candidates_data = [
            ["Claudio Ranieri", "Allenatore", "72", "Italia", "30 anni", "88/100", "€1.5M/anno", "Disponibile"],
            ["Gennaro Gattuso", "Allenatore", "45", "Italia", "8 anni", "82/100", "€1.2M/anno", "Interessato"],
            ["Sinisa Mihajlovic", "Vice Allenatore", "54", "Serbia", "15 anni", "85/100", "€600K/anno", "Disponibile"],
            ["Walter Zenga", "All. Portieri", "63", "Italia", "20 anni", "90/100", "€400K/anno", "Disponibile"],
            ["Demetrio Albertini", "Scout", "51", "Italia", "12 anni", "87/100", "€80K/anno", "Interessato"]
        ]
        
        candidates_tree = self.create_data_table(results_frame, candidates_columns, candidates_data, height=8)
        
        # Pulsanti ricerca staff
        search_buttons = [
            {'text': '📋 Dettagli Candidato', 'command': self._candidate_details, 'style': 'Primary.TButton'},
            {'text': '💼 Fai Offerta', 'command': self._make_offer, 'style': 'Primary.TButton'},
            {'text': '📞 Contatta Agente', 'command': self._contact_agent, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(results_frame, search_buttons)
    
    # Metodi placeholder per le azioni
    def _staff_details(self):
        self.show_message("Dettagli Staff", "Funzionalità in sviluppo", "info")
    
    def _renew_contract(self):
        self.show_message("Rinnova Contratto", "Funzionalità in sviluppo", "info")
    
    def _replace_staff(self):
        self.show_message("Sostituisci Staff", "Funzionalità in sviluppo", "info")
    
    def _enroll_course(self):
        self.show_message("Iscriviti Corso", "Funzionalità in sviluppo", "info")
    
    def _development_plan(self):
        self.show_message("Piano Sviluppo", "Funzionalità in sviluppo", "info")
    
    def _new_treatment(self):
        self.show_message("Nuovo Trattamento", "Funzionalità in sviluppo", "info")
    
    def _medical_report(self):
        self.show_message("Report Medico", "Funzionalità in sviluppo", "info")
    
    def _manage_medications(self):
        self.show_message("Gestione Farmaci", "Funzionalità in sviluppo", "info")
    
    def _report_details(self):
        self.show_message("Dettagli Report", "Funzionalità in sviluppo", "info")
    
    def _new_mission(self):
        self.show_message("Nuova Missione", "Funzionalità in sviluppo", "info")
    
    def _assign_scout(self):
        self.show_message("Assegna Scout", "Funzionalità in sviluppo", "info")
    
    def _candidate_details(self):
        self.show_message("Dettagli Candidato", "Funzionalità in sviluppo", "info")
    
    def _make_offer(self):
        self.show_message("Fai Offerta", "Funzionalità in sviluppo", "info")
    
    def _contact_agent(self):
        self.show_message("Contatta Agente", "Funzionalità in sviluppo", "info")
