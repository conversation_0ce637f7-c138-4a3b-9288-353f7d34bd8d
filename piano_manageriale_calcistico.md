# Piano Manageriale Calcistico - <PERSON><PERSON>lo Presidente/Dirigente

## Panoramica del Progetto

Questo documento descrive l'architettura completa per un gioco manageriale di calcio dove il giocatore assume il ruolo di **Presidente/Dirigente** del club, focalizzandosi su decisioni strategiche, finanziarie e gestionali, mentre l'aspetto tattico è delegato all'intelligenza artificiale dell'allenatore.

## Analisi Dati Esistenti

Il progetto si basa sui seguenti dati già disponibili nella cartella `data/`:

- **Database nomi/cognomi** per 50+ nazioni con realismo geografico (`player_names.json`)
- **Calendari competizioni europee** dettagliati (`european_calendar.json`)
- **Confederazioni FIFA** complete per generazione giocatori mondiali (`fifa_confederations.json`)
- **Codici paese FIFA** per mappatura nazionale (`fifa_country_codes.json`)
- **Strutture campionati** con 15+ nazioni europee e Serie A/B italiane dettagliate
- **Specifiche tecniche** per budget, reputazione, infrastrutture club

---

## Architettura Modulare Dettagliata

*Ogni modulo è progettato per contenere 60-120 righe di codice, mantenendo alta leggibilità e manutenibilità.*

### 1. **Core Business** - Gestione Club
```
src/club/
├── club.py                 # Classe base club (80 righe)
├── club_manager.py         # Gestione operazioni club (100 righe)
├── finances.py             # Budget e contabilità (90 righe)
├── facilities.py           # Infrastrutture (stadio, centro allenamento) (85 righe)
├── reputation.py           # Sistema reputazione e attrattività (75 righe)
├── sponsors.py             # Gestione contratti sponsor (95 righe)
├── merchandising.py        # Vendita gadget e magliette (70 righe)
├── ticket_sales.py         # Vendita biglietti e abbonamenti (80 righe)
└── club_history.py         # Storia e palmares del club (65 righe)
```

### 2. **Staff Management** - Gestione Personale
```
src/staff/
├── staff_base.py           # Classe base staff (60 righe)
├── coaching_staff.py       # Allenatore principale (110 righe)
├── assistant_coaches.py    # Vice allenatori (85 righe)
├── fitness_staff.py        # Preparatori atletici (75 righe)
├── goalkeeping_coach.py    # Allenatore portieri (70 righe)
├── scouts.py              # Osservatori (90 righe)
├── chief_scout.py         # Capo scouting (80 righe)
├── medical_staff.py       # Staff medico (85 righe)
├── physiotherapist.py     # Fisioterapisti (70 righe)
├── psychologist.py        # Psicologo sportivo (65 righe)
├── nutritionist.py        # Nutrizionista (60 righe)
├── technical_director.py  # Direttore tecnico (95 righe)
├── youth_coordinator.py   # Coordinatore settore giovanile (80 righe)
├── staff_generator.py     # Generazione staff realistici (100 righe)
└── staff_contracts.py     # Contratti e rinnovi staff (85 righe)
```

### 3. **Player System** - Sistema Giocatori
```
src/players/
├── player.py              # Classe giocatore base (100 righe)
├── player_attributes.py   # Attributi fisici e tecnici (90 righe)
├── player_position.py     # Posizioni e ruoli (70 righe)
├── player_generator.py    # Generazione giocatori realistici (120 righe)
├── player_development.py  # Sviluppo e crescita (85 righe)
├── player_form.py         # Forma fisica e morale (75 righe)
├── player_injuries.py     # Sistema infortuni (80 righe)
├── youth_academy.py       # Settore giovanile (110 righe)
├── youth_players.py       # Gestione giovani (90 righe)
├── loan_system.py         # Sistema prestiti (95 righe)
├── transfer_market.py     # Mercato trasferimenti (120 righe)
├── transfer_negotiations.py # Negoziazioni trasferimenti (100 righe)
├── contracts.py           # Contratti base (85 righe)
├── contract_negotiations.py # Negoziazioni contrattuali (95 righe)
├── player_wages.py        # Gestione stipendi (80 righe)
└── retirement.py          # Ritiro giocatori (70 righe)
```

### 4. **Competition Engine** - Motore Competizioni
```
src/competitions/
├── competition_base.py     # Classe base competizione (80 righe)
├── league_manager.py       # Gestione campionati (110 righe)
├── league_table.py         # Classifiche (85 righe)
├── league_fixtures.py      # Calendario campionati (90 righe)
├── european_cups.py        # Coppe europee generiche (100 righe)
├── champions_league.py     # Champions League specifica (95 righe)
├── europa_league.py        # Europa League specifica (90 righe)
├── conference_league.py    # Conference League specifica (85 righe)
├── cup_competitions.py     # Coppe nazionali (95 righe)
├── international_cups.py   # Coppe internazionali (80 righe)
├── friendly_matches.py     # Partite amichevoli (75 righe)
├── match_engine.py         # Simulazione partite (120 righe)
├── match_events.py         # Eventi durante la partita (100 righe)
├── match_statistics.py     # Statistiche partite (85 righe)
├── referee_system.py       # Sistema arbitrale (70 righe)
├── calendar_manager.py     # Calendario generale (110 righe)
├── fixture_generator.py    # Generatore fixture (90 righe)
├── qualification_system.py # Sistema qualificazioni europee (85 righe)
└── promotion_relegation.py # Promozioni/retrocessioni (80 righe)
```

### 5. **Tactical System** - Sistema Tattico (Delegato all'IA)
```
src/tactics/
├── formation_system.py    # Sistema formazioni (85 righe)
├── team_instructions.py   # Istruzioni tattiche (90 righe)
├── player_roles.py        # Ruoli tattici giocatori (80 righe)
├── set_pieces.py          # Calci piazzati (75 righe)
├── substitution_logic.py  # Logica sostituzioni (85 righe)
├── tactical_analysis.py   # Analisi tattica (90 righe)
└── opponent_analysis.py   # Analisi avversari (80 righe)
```

### 6. **AI Strategy** - Intelligenza Artificiale
```
src/ai/
├── ai_base.py             # Classe base IA (70 righe)
├── board_ai.py            # IA Consiglio Amministrazione (100 righe)
├── coach_ai.py            # IA Allenatore (120 righe)
├── coach_decisions.py     # Decisioni tattiche allenatore (95 righe)
├── player_ai.py           # IA comportamento giocatori (85 righe)
├── market_ai.py           # IA altri club mercato (110 righe)
├── negotiation_ai.py      # IA negoziazioni (90 righe)
├── media_ai.py            # IA reazioni media (80 righe)
├── fan_ai.py              # IA tifosi (75 righe)
├── agent_ai.py            # IA procuratori (85 righe)
├── rival_clubs_ai.py      # IA club rivali (90 righe)
└── personality_system.py  # Sistema personalità (80 righe)
```

### 7. **Data Management** - Gestione Dati
```
src/data/
├── data_loader.py         # Caricamento dati JSON (90 righe)
├── league_loader.py       # Caricamento campionati (85 righe)
├── competition_loader.py  # Caricamento competizioni (80 righe)
├── name_generator.py      # Generazione nomi realistici (100 righe)
├── country_data.py        # Gestione dati nazionali (75 righe)
├── city_data.py           # Gestione dati città (70 righe)
├── stadium_data.py        # Gestione dati stadi (75 righe)
├── save_manager.py        # Salvataggio partite (95 righe)
├── load_manager.py        # Caricamento partite (85 righe)
├── database_manager.py    # Gestione database SQLite (110 righe)
├── statistics_db.py       # Database statistiche (90 righe)
├── export_manager.py      # Esportazione dati (80 righe)
└── backup_manager.py      # Backup automatici (75 righe)
```

### 8. **Game Logic** - Logica di Gioco
```
src/game/
├── game_engine.py         # Motore principale di gioco (120 righe)
├── game_state.py          # Gestione stato di gioco (90 righe)
├── season_manager.py      # Gestione stagioni (100 righe)
├── month_manager.py       # Gestione operazioni mensili (85 righe)
├── week_manager.py        # Gestione operazioni settimanali (80 righe)
├── day_manager.py         # Gestione operazioni giornaliere (75 righe)
├── event_system.py        # Sistema eventi casuali (95 righe)
├── random_events.py       # Eventi random specifici (110 righe)
├── objectives.py          # Obiettivi stagionali (85 righe)
├── board_objectives.py    # Obiettivi del consiglio (80 righe)
├── fan_expectations.py    # Aspettative tifosi (75 righe)
├── pressure_system.py     # Sistema pressione (85 righe)
└── difficulty_manager.py  # Gestione livelli difficoltà (70 righe)
```

### 9. **Financial System** - Sistema Finanziario
```
src/finances/
├── budget_manager.py      # Gestione budget generale (100 righe)
├── revenue_system.py      # Sistema ricavi (95 righe)
├── expense_manager.py     # Gestione spese (90 righe)
├── transfer_budget.py     # Budget trasferimenti (85 righe)
├── wage_budget.py         # Budget stipendi (80 righe)
├── tax_system.py          # Sistema tasse (75 righe)
├── loan_system.py         # Prestiti bancari (85 righe)
├── investment_system.py   # Sistema investimenti (80 righe)
├── sponsor_deals.py       # Contratti sponsor (90 righe)
├── prize_money.py         # Premi competizioni (75 righe)
├── gate_receipts.py       # Incassi botteghino (70 righe)
└── financial_reports.py   # Report finanziari (85 righe)
```

### 10. **Media & Communication** - Media e Comunicazione
```
src/media/
├── media_system.py        # Sistema media generale (90 righe)
├── press_conferences.py   # Conferenze stampa (95 righe)
├── interview_system.py    # Sistema interviste (85 righe)
├── news_generator.py      # Generatore notizie (100 righe)
├── rumors_system.py       # Sistema voci/rumors (80 righe)
├── social_media.py        # Gestione social media (75 righe)
├── fan_reactions.py       # Reazioni tifosi (85 righe)
├── media_coverage.py      # Copertura mediatica (80 righe)
└── public_relations.py    # Relazioni pubbliche (75 righe)
```

### 11. **Statistics & Analytics** - Statistiche e Analisi
```
src/statistics/
├── player_stats.py        # Statistiche giocatori (95 righe)
├── team_stats.py          # Statistiche squadra (90 righe)
├── match_stats.py         # Statistiche partite (85 righe)
├── season_stats.py        # Statistiche stagionali (80 righe)
├── historical_stats.py    # Statistiche storiche (100 righe)
├── performance_analysis.py # Analisi performance (95 righe)
├── scouting_reports.py    # Report scouting (90 righe)
├── medical_reports.py     # Report medici (75 righe)
└── analytics_engine.py    # Motore analitico (110 righe)
```

### 12. **User Interface Logic** - Logica Interfaccia (Non GUI)
```
src/interface/
├── menu_system.py         # Sistema menu testuale (90 righe)
├── screen_manager.py      # Gestione schermate (85 righe)
├── input_handler.py       # Gestione input utente (80 righe)
├── display_manager.py     # Gestione visualizzazione (95 righe)
├── notification_system.py # Sistema notifiche (75 righe)
├── alert_system.py        # Sistema avvisi (70 righe)
├── confirmation_dialogs.py # Dialog conferme (75 righe)
└── help_system.py         # Sistema aiuto (80 righe)
```

---

## Meccaniche Specifiche Ruolo Presidente

### Responsabilità Principali del Giocatore:

#### 1. **Gestione Finanziaria Strategica**
- Approvazione budget trasferimenti annuali
- Gestione stipendi e masse salariali
- Decisioni su rinnovi contrattuali costosi
- Investimenti in infrastrutture (stadio, centro allenamento)
- Negoziazione contratti sponsor
- Approvazione prestiti bancari

#### 2. **Scelte Strategiche a Lungo Termine**
- Assunzione/licenziamento allenatore
- Assunzione direttore tecnico e staff dirigenziale
- Definizione filosofia di gioco del club
- Investimenti settore giovanile
- Strategia di mercato (giovani vs esperti)
- Espansione stadio e merchandising

#### 3. **Relazioni Esterne e Comunicazione**
- Gestione rapporti con tifosi
- Conferenze stampa presidenziali
- Negoziazioni con comuni per nuovo stadio
- Gestione crisi mediatiche
- Rapporti con altri presidenti

#### 4. **Supervisione Sportiva**
- Definizione obiettivi stagionali
- Approvazione trasferimenti sopra soglia
- Monitoraggio performance allenatore
- Interventi in caso di crisi risultati
- Decisioni su partecipazione competizioni

### Deleghe all'IA dell'Allenatore:
- Scelte tattiche e formazioni
- Sostituzioni durante le partite
- Gestione allenamenti quotidiani
- Motivazione giocatori pre-partita
- Gestione rotazioni squadra

---

## Sistema Generazione Nomi Realistici

### Implementazione Basata sui Dati Esistenti:

#### **Giocatori**
- Utilizzo `player_names.json` per combinazioni nome+cognome realistic
- Generazione per nazione di nascita del giocatore
- Sistema di probabilità per nomi comuni vs rari
- Compatibilità con `fifa_confederations.json` per distribuzione geografica

#### **Staff Tecnico**
- Database separato con cognomi storici del calcio
- Generazione basata su nazionalità e esperienza
- Nomi più comuni per staff di supporto

#### **Dirigenti e Procuratori**
- Utilizzo nomi formali per dirigenti
- Sistema cognomi "importanti" per procuratori influenti

---

## Gestione Competizioni

### **Competizioni Nazionali**
- **Serie A/Serie B** come base italiana completa
- **15+ campionati europei** con sistema promozioni/retrocessioni
- **Coppe nazionali** per ogni paese implementato
- **Sistema coefficienti UEFA** per qualificazioni europee

### **Competizioni Europee**
- **Champions League** con calendario realistico da `european_calendar.json`
- **Europa League** e **Conference League** complete
- **Sistema qualificazioni** basato su posizioni campionato precedente
- **Teste di serie** e sorteggi realistici

### **Competizioni Amichevoli**
- **Tornei estivi** per preparazione
- **Amichevoli internazionali** durante soste
- **Partite di gala** per fundraising

---

## Sistema di Sviluppo e Implementazione

### **Fase 1: Core Foundation (2-3 settimane)**
- Implementazione classi base (club, player, staff)
- Sistema caricamento dati JSON esistenti
- Motore di gioco base con avanzamento temporale

### **Fase 2: Competition Engine (2-3 settimane)**
- Sistema campionati e coppe
- Calendario automatico e fixture
- Simulazione partite base

### **Fase 3: Management Systems (3-4 settimane)**
- Sistema trasferimenti e contratti
- IA allenatore per decisioni tattiche
- Sistema finanziario completo

### **Fase 4: Advanced Features (2-3 settimane)**
- Sistema media e comunicazione
- Eventi casuali e sistema pressione
- Statistiche avanzate e analytics

### **Fase 5: Polish & Balance (1-2 settimane)**
- Bilanciamento difficoltà
- Test e debugging
- Documentazione utente

---

## Tecnologie e Dipendenze

### **Linguaggio Base**
- **Python 3.9+** per compatibilità e semplicità

### **Database**
- **SQLite** per persistenza dati
- **JSON** per configurazioni e dati statici

### **Librerie Consigliate**
- `json` (standard) - Gestione dati
- `sqlite3` (standard) - Database
- `random` (standard) - Generazione casuale
- `datetime` (standard) - Gestione tempo
- `dataclasses` (standard) - Strutture dati
- `typing` (standard) - Type hints

### **Struttura Project**
```
football_manager/
├── src/                    # Codice sorgente
├── data/                   # Dati JSON esistenti
├── saves/                  # Salvataggi utente
├── config/                 # Configurazioni
├── tests/                  # Test automatici
├── docs/                   # Documentazione
├── requirements.txt        # Dipendenze Python
└── main.py                # Entry point applicazione
```

---

## Stato Implementazione

### ✅ **COMPLETATI** (Fase 1 - Core Foundation)

#### **1. Struttura Directory**
- Creata struttura modulare completa con tutte le directory previste
- Organizzazione in 12 categorie principali come da piano

#### **2. Game Engine (`src/game/game_engine.py`)** - **120 righe**
- Motore principale con gestione stati di gioco
- Sistema avanzamento temporale (giorno/settimana/mese)
- Gestione fasi stagionali (pre-season, season, winter break, end season)
- Sistema eventi programmati
- Controllo velocità e pausa gioco
- Salvataggio/caricamento (placeholder per moduli futuri)

#### **3. Data Loader (`src/data/data_loader.py`)** - **95 righe**
- Caricamento completo dati JSON esistenti
- Gestione campionati UEFA (15+ nazioni)
- Accesso dati club, nomi giocatori, confederazioni
- Validazione integrità dati
- Metodi ricerca club per nome/paese

#### **4. Club Base (`src/club/club.py`)** - **115 righe**
- Classe completa con finanze, facilities, reputazione
- Gestione budget trasferimenti/stipendi
- Sistema trofei e storico club
- Statistiche campionato in tempo reale
- Calcolo valore club e form
- Strutture dati per giocatori e staff

#### **5. Player System (`src/players/player.py`)** - **145 righe**
- Sistema completo attributi (tecnici/mentali/fisici)
- Gestione posizioni e ruoli
- Calcolo rating per posizione
- Contratti e statistiche carriera
- Sistema forma/morale/fitness
- Valore di mercato dinamico

#### **6. Name Generator (`src/data/name_generator.py`)** - **120 righe**
- Generazione nomi realistici per 50+ nazioni
- Specializzazione per ruoli (allenatori, staff, agenti)
- Sistema anti-duplicati
- Nickname per nazionalità specifiche (es. brasiliani)
- Cognomi famosi allenatori per realismo

#### **7. Main Entry Point (`main.py`)** - **60 righe**
- Demo funzionale dei sistemi implementati
- Test caricamento dati
- Esempio creazione club e generazione nomi
- Validazione integrità sistema

---

## 📋 **GUIDA SVILUPPO** - Cosa Fare e Come Procedere

### **MIGLIORAMENTI AI MODULI ESISTENTI** 

#### **1. Game Engine (`src/game/game_engine.py`)** - **PRIORITÀ ALTA**
**Cosa Aggiungere:**
- [ ] Metodo `_initialize_competitions()` - Carica campionati da DataLoader
- [ ] Metodo `_set_club_objectives()` - Imposta obiettivi stagionali per ogni club
- [ ] Metodo `_process_match()` - Integrazione con match_engine quando sarà creato
- [ ] Metodo `_process_board_meeting()` - Riunioni consiglio ogni 2 settimane
- [ ] Classe `Event` per eventi programmati (attualmente solo placeholder)
- [ ] Sistema salvataggio automatico ogni fine settimana

**Come Procedere:**
```python
# Aggiungere alla fine del file
class Event:
    def __init__(self, date, event_type, data):
        self.date = date
        self.type = event_type
        self.data = data

def _initialize_competitions(self):
    # Usare self.data_loader per caricare campionati
    # Creare istanze League e Cup dai JSON
```

#### **2. Data Loader (`src/data/data_loader.py`)** - **PRIORITÀ MEDIA**
**Cosa Aggiungere:**
- [ ] Metodo `get_league_by_level()` - Trova campionato per livello (1=Serie A, 2=Serie B)
- [ ] Metodo `get_european_qualified_clubs()` - Club qualificati coppe europee
- [ ] Cache per dati frequentemente acceduti
- [ ] Validazione più specifica per dati corrotti
- [ ] Metodo `reload_data()` per aggiornamenti runtime

**Come Procedere:**
```python
def get_league_by_level(self, country: str, level: int) -> Optional[Dict]:
    """Find league by competition level (1=top division)"""
    if country in self.leagues_data:
        for league_data in self.leagues_data[country].values():
            if league_data.get('livello_competizione') == level:
                return league_data
    return None
```

#### **3. Club (`src/club/club.py`)** - **PRIORITÀ ALTA**
**Cosa Aggiungere:**
- [ ] Metodo `add_player()` e `remove_player()` - Gestione rosa
- [ ] Metodo `calculate_wage_bill()` - Calcolo monte stipendi
- [ ] Metodo `update_reputation()` - Aggiornamento reputazione basato su risultati
- [ ] Sistema `board_pressure` - Pressione del consiglio
- [ ] Metodi per gestione staff (`add_staff_member()`, `fire_staff()`)
- [ ] Sistema `fan_happiness` - Contentezza tifosi

**Come Procedere:**
```python
def add_player(self, player):
    """Add player to squad"""
    if len(self.players) < 25:  # Squad limit
        self.players.append(player)
        player.current_club = self.name
        return True
    return False

def calculate_wage_bill(self) -> int:
    """Calculate total weekly wage bill"""
    return sum(player.contract.wage for player in self.players if player.contract)
```

#### **4. Player (`src/players/player.py`)** - **PRIORITÀ MEDIA**
**Cosa Aggiungere:**
- [ ] Metodo `develop_attributes()` - Crescita attributi basata su età/allenamento
- [ ] Sistema `injury_system` - Gestione infortuni con tempi recupero
- [ ] Metodo `calculate_morale_change()` - Variazione morale basata su eventi
- [ ] Sistema `transfer_interest` - Interesse altri club
- [ ] Metodo `negotiate_contract()` - Negoziazione contratti

**Come Procedere:**
```python
def develop_attributes(self, training_quality: int):
    """Develop player attributes based on training"""
    if self.age < 24:  # Young players develop faster
        development_points = training_quality * random.uniform(0.5, 1.5)
        # Distribute points across attributes
```

#### **5. Name Generator (`src/data/name_generator.py`)** - **PRIORITÀ BASSA**
**Cosa Aggiungere:**
- [ ] Generazione nomi per altre confederazioni (CAF, AFC, CONCACAF)
- [ ] Sistema peso per nomi (più/meno comuni)
- [ ] Nomi femminili per staff amministrativo
- [ ] Generazione nomi storici per leggende
- [ ] Metodo `generate_realistic_age_name()` - Nomi adatti all'età

---

### **NUOVI MODULI DA CREARE** - **Roadmap Prioritizzata**

### **🔥 FASE 2A - Staff & AI Systems** (Settimana 1-2)

#### **1. `src/staff/coaching_staff.py`** - **CRITICO**
```python
class Coach:
    def __init__(self, name, nationality, preferred_formation):
        self.name = name
        self.nationality = nationality
        self.preferred_formation = preferred_formation
        self.tactical_knowledge = {}  # Conoscenza formazioni
        self.reputation = 0  # 1-20
        self.contract = None
        self.morale = 50
        
    def select_formation(self, opponent_strength):
        """AI selects formation based on opponent"""
        pass
        
    def make_substitutions(self, match_state):
        """AI decides substitutions during match"""
        pass
```

#### **2. `src/ai/coach_ai.py`** - **CRITICO**
```python
class CoachAI:
    def __init__(self, coach, team_data):
        self.coach = coach
        self.team = team_data
        self.tactical_preference = self._determine_preference()
        
    def select_starting_eleven(self, available_players):
        """Select best formation and players"""
        pass
        
    def adjust_tactics(self, match_minute, current_score):
        """Dynamic tactical changes during match"""
        pass
```

#### **3. `src/staff/staff_generator.py`** - **MEDIO**
```python
def generate_coach(nationality: str, experience_level: str) -> Coach:
    """Generate realistic coach with appropriate stats"""
    pass
    
def generate_staff_team(club_reputation: int) -> List[Staff]:
    """Generate complete staff team for club"""
    pass
```

### **🏆 FASE 2B - Competition Engine** (Settimana 2-3)

#### **4. `src/competitions/league_manager.py`** - **CRITICO**
```python
class League:
    def __init__(self, league_data, clubs):
        self.name = league_data['nome_campionato']
        self.clubs = [Club(club_data) for club_data in clubs]
        self.table = self._initialize_table()
        self.fixtures = []
        
    def generate_fixtures(self):
        """Generate full season fixture list"""
        pass
        
    def update_table(self, match_result):
        """Update league table after match"""
        pass
        
    def get_european_qualifiers(self):
        """Get clubs qualified for European competitions"""
        pass
```

#### **5. `src/competitions/match_engine.py`** - **CRITICO**
```python
class MatchEngine:
    def simulate_match(self, home_team, away_team):
        """Simulate match and return result"""
        result = {
            'home_goals': 0,
            'away_goals': 0,
            'events': [],
            'statistics': {}
        }
        return result
        
    def _calculate_team_strength(self, team, formation):
        """Calculate team strength for simulation"""
        pass
```

#### **6. `src/competitions/calendar_manager.py`** - **ALTO**
```python
class CalendarManager:
    def __init__(self, european_calendar_data):
        self.european_dates = european_calendar_data
        self.season_calendar = {}
        
    def create_season_calendar(self, leagues, european_cups):
        """Create integrated calendar for all competitions"""
        pass
        
    def check_fixture_conflicts(self, date, team):
        """Ensure no team plays twice in same period"""
        pass
```

### **💰 FASE 2C - Financial Systems** (Settimana 3-4)

#### **7. `src/finances/budget_manager.py`** - **CRITICO**
```python
class BudgetManager:
    def __init__(self, club):
        self.club = club
        self.monthly_income = 0
        self.monthly_expenses = 0
        
    def process_monthly_finances(self):
        """Process all monthly financial transactions"""
        pass
        
    def calculate_transfer_budget(self):
        """Calculate available transfer budget"""
        pass
        
    def approve_expense(self, amount, category):
        """Check if club can afford expense"""
        pass
```

#### **8. `src/players/transfer_market.py`** - **ALTO**
```python
class TransferMarket:
    def __init__(self):
        self.available_players = []
        self.transfer_window_open = False
        
    def list_player_for_sale(self, player, asking_price):
        """List player on transfer market"""
        pass
        
    def submit_bid(self, buying_club, player, offer_amount):
        """Submit transfer bid"""
        pass
        
    def negotiate_transfer(self, bid):
        """AI negotiates transfer between clubs"""
        pass
```

#### **9. `src/club/sponsors.py`** - **MEDIO**
```python
class SponsorManager:
    def __init__(self, club):
        self.club = club
        self.current_deals = []
        
    def generate_sponsor_offers(self):
        """Generate sponsor offers based on club reputation"""
        pass
        
    def calculate_deal_value(self, sponsor_type, club_performance):
        """Calculate sponsor deal value"""
        pass
```

### **🎮 FASE 2D - Game Systems** (Settimana 4-5)

#### **10. `src/game/season_manager.py`** - **CRITICO**
```python
class SeasonManager:
    def __init__(self, game_engine):
        self.game = game_engine
        self.current_matchday = 0
        
    def advance_matchday(self):
        """Process all matches for current matchday"""
        pass
        
    def end_season(self):
        """Process season end - promotions, trophies, etc."""
        pass
```

#### **11. `src/interface/menu_system.py`** - **ALTO**
```python
class MenuSystem:
    def __init__(self, game_engine):
        self.game = game_engine
        
    def show_main_menu(self):
        """Display main game menu"""
        pass
        
    def show_club_overview(self):
        """Show club status screen"""
        pass
        
    def handle_president_decisions(self):
        """Handle presidential decision menus"""
        pass
```

---

## **📖 GUIDA PER SVILUPPATORI**

### **Come Contribuire:**

1. **Scegliere un modulo** dalla lista priorità
2. **Studiare i moduli esistenti** per capire lo stile del codice
3. **Rispettare il limite di 60-120 righe** per modulo
4. **Usare i data esistenti** nella cartella `data/`
5. **Testare con `main.py`** prima di commit

### **Regole di Codice:**
- **Docstrings** per tutte le classi e metodi pubblici
- **Type hints** per parametri e return
- **Dataclasses** per strutture dati semplici
- **Enums** per costanti e stati
- **Gestione errori** con try/except appropriati

### **Testing:**
```bash
python main.py  # Test sistema base
python -m pytest tests/  # Test automatici (da creare)
```

### **Struttura Import:**
```python
# Standard library
from datetime import datetime
from typing import Dict, List

# Local imports
from ..data.data_loader import DataLoader
from .club import Club
```

---

## Test Sistema Attuale

Per testare i moduli implementati:

```bash
cd "C:\Users\<USER>\Desktop\google cli"
python main.py
```

Il sistema caricherà i dati esistenti e dimostrerà:
- ✅ Caricamento 15+ campionati UEFA
- ✅ Generazione nomi realistici per 50+ nazioni  
- ✅ Creazione club con dati finanziari/reputazione
- ✅ Validazione integrità dati JSON

---

## Conclusioni

Questo piano fornisce una struttura modulare completa per un manageriale calcistico focalizzato sul ruolo presidenziale, mantenendo ogni modulo sotto le 120 righe per garantire manutenibilità e leggibilità del codice. La separazione delle responsabilità permette sviluppo incrementale e testing isolato di ogni componente.

L'utilizzo dei dati esistenti nella cartella `data/` garantisce realismo immediato, mentre l'architettura modulare permette espansioni future senza impattare il core del sistema.