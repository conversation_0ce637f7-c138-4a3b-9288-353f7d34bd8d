"""
Menu principale del Football Manager
Schermata iniziale con le opzioni principali del gioco
"""

import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
from .base_window import BaseWindow
from .gui_config import COLORS, FONTS, COMPONENT_SIZES, TEXTS, ICONS


class MainMenu(BaseWindow):
    """Finestra del menu principale"""
    
    def __init__(self, parent: tk.Widget):
        super().__init__(parent, TEXTS['app_title'])
        self.logo_image = None
        
    def setup_ui(self):
        """Configura l'interfaccia utente del menu principale"""
        if not self.window:
            return
            
        # Frame principale
        main_frame = ttk.Frame(self.window, style='Main.TFrame')
        main_frame.pack(fill='both', expand=True)
        
        # Crea le sezioni del menu
        self._create_header_section(main_frame)
        self._create_menu_section(main_frame)
        self._create_footer_section(main_frame)
    
    def _create_header_section(self, parent: tk.Widget):
        """Crea la sezione header con logo e titolo"""
        header_frame = ttk.Frame(parent, style='Section.TFrame')
        header_frame.pack(fill='x', padx=20, pady=20)
        
        # Contenitore per logo e titolo
        title_container = ttk.Frame(header_frame)
        title_container.pack(expand=True)
        
        # Logo (placeholder - può essere sostituito con un'immagine reale)
        logo_frame = ttk.Frame(title_container)
        logo_frame.pack(pady=(20, 10))
        
        # Icona del calcio come logo temporaneo
        logo_label = ttk.Label(
            logo_frame, 
            text="⚽", 
            font=('Arial', 48),
            foreground=COLORS['primary']
        )
        logo_label.pack()
        
        # Titolo principale
        title_label = ttk.Label(
            title_container,
            text=TEXTS['app_title'],
            style='Title.TLabel'
        )
        title_label.pack(pady=(0, 10))
        
        # Sottotitolo
        subtitle_label = ttk.Label(
            title_container,
            text="Gestisci il tuo club come un vero presidente",
            font=FONTS['subtitle'],
            foreground=COLORS['text_secondary']
        )
        subtitle_label.pack()
    
    def _create_menu_section(self, parent: tk.Widget):
        """Crea la sezione con i pulsanti del menu"""
        menu_frame = ttk.Frame(parent)
        menu_frame.pack(expand=True, fill='both', padx=20, pady=20)
        
        # Contenitore per i pulsanti centrati
        buttons_container = ttk.Frame(menu_frame)
        buttons_container.pack(expand=True)
        
        # Configurazione dei pulsanti del menu
        menu_buttons = [
            {
                'text': f"{ICONS['new']} {TEXTS['menu_main']['new_game']}",
                'command': self._on_new_game,
                'style': 'Primary.TButton',
                'description': 'Inizia una nuova carriera manageriale'
            },
            {
                'text': f"{ICONS['load']} {TEXTS['menu_main']['load_game']}",
                'command': self._on_load_game,
                'style': 'Secondary.TButton',
                'description': 'Continua una partita salvata'
            },
            {
                'text': f"{ICONS['settings']} {TEXTS['menu_main']['settings']}",
                'command': self._on_settings,
                'style': 'Secondary.TButton',
                'description': 'Configura le impostazioni del gioco'
            },
            {
                'text': f"{ICONS['exit']} {TEXTS['menu_main']['exit']}",
                'command': self._on_exit,
                'style': 'Secondary.TButton',
                'description': 'Esci dal gioco'
            }
        ]
        
        # Crea i pulsanti
        for i, button_config in enumerate(menu_buttons):
            # Frame per ogni pulsante con descrizione
            button_frame = ttk.Frame(buttons_container)
            button_frame.pack(pady=10, fill='x')
            
            # Pulsante principale
            btn = ttk.Button(
                button_frame,
                text=button_config['text'],
                command=button_config['command'],
                style=button_config['style'],
                width=30
            )
            btn.pack()
            
            # Descrizione del pulsante
            desc_label = ttk.Label(
                button_frame,
                text=button_config['description'],
                font=FONTS['small'],
                foreground=COLORS['text_secondary']
            )
            desc_label.pack(pady=(5, 0))
            
            # Effetti hover
            self._add_button_hover_effects(btn)
    
    def _create_footer_section(self, parent: tk.Widget):
        """Crea la sezione footer con informazioni"""
        footer_frame = ttk.Frame(parent, style='Section.TFrame')
        footer_frame.pack(fill='x', side='bottom', padx=20, pady=10)
        
        # Informazioni versione
        version_label = ttk.Label(
            footer_frame,
            text="Football Manager - Edizione Presidenziale v1.0",
            font=FONTS['small'],
            foreground=COLORS['text_secondary']
        )
        version_label.pack(side='left')
        
        # Copyright
        copyright_label = ttk.Label(
            footer_frame,
            text="© 2025 Football Manager Team",
            font=FONTS['small'],
            foreground=COLORS['text_secondary']
        )
        copyright_label.pack(side='right')
    
    def _add_button_hover_effects(self, button: ttk.Button):
        """Aggiunge effetti hover ai pulsanti"""
        def on_enter(event):
            button.configure(cursor='hand2')
        
        def on_leave(event):
            button.configure(cursor='')
        
        button.bind('<Enter>', on_enter)
        button.bind('<Leave>', on_leave)
    
    def _on_new_game(self):
        """Gestisce il click su Nuovo Gioco"""
        self.trigger_callback('new_game')
    
    def _on_load_game(self):
        """Gestisce il click su Carica Partita"""
        self.trigger_callback('load_game')
    
    def _on_settings(self):
        """Gestisce il click su Impostazioni"""
        self.trigger_callback('settings')
    
    def _on_exit(self):
        """Gestisce il click su Esci"""
        self.trigger_callback('exit')
    
    def setup_events(self):
        """Configura gli eventi della finestra"""
        if not self.window:
            return
            
        # Gestione tasti scorciatoia
        self.window.bind('<Return>', lambda e: self._on_new_game())
        self.window.bind('<Escape>', lambda e: self._on_exit())
        
        # Focus sulla finestra
        self.window.focus_set()
    
    def create_window(self, width: int = 600, height: int = 700, resizable: bool = False):
        """Crea la finestra del menu principale"""
        window = super().create_window(width, height, resizable)
        
        # Configura il colore di sfondo
        window.configure(bg=COLORS['background'])
        
        return window
