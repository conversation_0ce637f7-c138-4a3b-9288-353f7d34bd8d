"""
Football Manager - Season Manager
Manages the progression of a football season, coordinating matches, transfers, and finances.
"""

from typing import List, Dict, Optional
from datetime import datetime, date, timedelta
import random

from game.game_engine import GameEngine
from competitions.league_manager import League
from competitions.match_engine import MatchEngine
from players.transfer_market import TransferMarket
from finances.budget_manager import <PERSON><PERSON>anager
from club.club import Club

class SeasonManager:
    def __init__(self, game_engine: GameEngine):
        self.game_engine = game_engine
        self.current_season = "2025/2026"
        self.leagues: List[League] = []
        self.match_engine = MatchEngine()
        self.transfer_market = TransferMarket()
        self.budget_managers: Dict[str, BudgetManager] = {}
        self.current_matchday = 0
        self.season_phase = "pre_season"  # pre_season, regular_season, winter_break, post_season
        
    def initialize_season(self, league_data_list: List[Dict]):
        """Initialize a new season with leagues"""
        # Create leagues
        for league_data in league_data_list:
            # Get clubs for this league
            clubs_data = league_data.get('clubs', [])
            clubs = [Club(club_data) for club_data in clubs_data]
            
            # Create league
            league = League(league_data, clubs)
            self.leagues.append(league)
            
            # Create budget managers for each club
            for club in clubs:
                budget_manager = BudgetManager(club)
                self.budget_managers[club.name] = budget_manager
                
        # Generate fixtures for all leagues
        for league in self.leagues:
            league.generate_fixtures()
            
        # Open summer transfer window
        self.transfer_market.open_transfer_window()
        
        print(f"Season {self.current_season} initialized with {len(self.leagues)} leagues")
        
    def advance_to_next_matchday(self) -> bool:
        """Advance to the next matchday and play all matches"""
        # Find the earliest matchday across all leagues
        earliest_matchday = None
        leagues_with_matches = []
        
        for league in self.leagues:
            if not league.is_season_finished():
                if earliest_matchday is None or league.current_matchday < earliest_matchday:
                    earliest_matchday = league.current_matchday
                    
        if earliest_matchday is None:
            # All leagues finished
            self.season_phase = "post_season"
            return False
            
        # Play matches for all leagues at this matchday
        matches_played = 0
        for league in self.leagues:
            if league.current_matchday == earliest_matchday:
                fixtures = league.get_current_matchday_fixtures()
                for fixture in fixtures:
                    if not fixture.played:
                        # Simulate match
                        result = self.match_engine.simulate_match(
                            fixture.home_team, 
                            fixture.away_team
                        )
                        
                        # Record result
                        league.play_match(fixture, result.home_goals, result.away_goals)
                        matches_played += 1
                        
                # Advance league to next matchday
                league.advance_matchday()
                
        print(f"Matchday {earliest_matchday} completed with {matches_played} matches")
        self.current_matchday = earliest_matchday
        return True
        
    def process_weekly_activities(self, week_date: date):
        """Process weekly activities like training, transfers, and finances"""
        # Process finances for all clubs
        for club_name, budget_manager in self.budget_managers.items():
            # Process monthly finances on the 1st of each month
            if week_date.day <= 7:  # Approximately monthly
                report = budget_manager.process_monthly_finances(week_date.month, week_date.year)
                print(f"Financial report for {club_name}: Net result €{report.net_result:,}")
                
        # Process transfer market bids
        if self.transfer_market.transfer_window_open:
            completed_transfers = self.transfer_market.process_bids()
            if completed_transfers:
                print(f"Completed {len(completed_transfers)} transfers this week")
                
        # Weekly training and player development would go here
        self._process_weekly_training()
        
    def _process_weekly_training(self):
        """Process weekly training and player development"""
        # Simplified training - in reality this would be more complex
        for league in self.leagues:
            for club in league.clubs:
                # Improve player fitness
                for player in club.players:
                    # Players recover fitness during the week
                    player.fitness = min(100, player.fitness + random.randint(1, 5))
                    
                    # Players can improve attributes through training
                    if random.random() < 0.1:  # 10% chance per week
                        # Select a random attribute to improve
                        attributes = ['pace', 'stamina', 'strength', 'agility', 'jumping_reach']
                        if hasattr(player.attributes, attributes[0]):  # Check if attributes exist
                            attr_to_improve = random.choice(attributes)
                            current_value = getattr(player.attributes, attr_to_improve)
                            if current_value < 20:  # Max is 20
                                setattr(player.attributes, attr_to_improve, current_value + 1)
                                
    def get_league_table(self, league_name: str) -> Optional[List]:
        """Get current table for a specific league"""
        for league in self.leagues:
            if league.name == league_name:
                return league.get_table()
        return None
        
    def get_top_scorers(self, league_name: str, limit: int = 10) -> List:
        """Get top scorers in a league (simplified)"""
        # This would normally track actual goals, but we'll simulate
        top_scorers = []
        for league in self.leagues:
            if league.name == league_name:
                for club in league.clubs:
                    # Add some players from each club
                    for player in club.players[:2]:  # First 2 players from each club
                        goals = random.randint(0, 20)  # Random goals
                        top_scorers.append({
                            'player': player,
                            'club': club,
                            'goals': goals
                        })
                break
                
        # Sort by goals and return top limit
        top_scorers.sort(key=lambda x: x['goals'], reverse=True)
        return top_scorers[:limit]
        
    def process_transfer_window(self):
        """Process the transfer window"""
        if not self.transfer_market.transfer_window_open:
            print("Transfer window is closed")
            return
            
        # Process all pending bids
        completed_transfers = self.transfer_market.process_bids()
        print(f"Transfer window processed: {len(completed_transfers)} transfers completed")
        
    def close_transfer_window(self):
        """Close the current transfer window"""
        self.transfer_market.close_transfer_window()
        print("Transfer window closed")
        
    def open_transfer_window(self):
        """Open a new transfer window"""
        self.transfer_market.open_transfer_window()
        print("Transfer window opened")
        
    def get_season_status(self) -> Dict:
        """Get current season status"""
        return {
            "season": self.current_season,
            "phase": self.season_phase,
            "current_matchday": self.current_matchday,
            "leagues": len(self.leagues),
            "transfer_window_open": self.transfer_market.transfer_window_open,
            "completed_transfers": len(self.transfer_market.completed_transfers)
        }
        
    def end_season(self):
        """Process end of season activities"""
        self.season_phase = "post_season"
        
        # Process end of season finances
        for budget_manager in self.budget_managers.values():
            # Add end of season bonuses
            club = budget_manager.club
            if club.league_position == 1:
                club.update_finances(revenue=5000000)  # League winners bonus
            elif club.league_position <= 4:
                club.update_finances(revenue=2000000)  # Top 4 bonus
                
        # Handle European qualification
        self._process_european_qualification()
        
        # Handle relegation/promotion
        self._process_relegation_promotion()
        
        print("Season ended. Processing complete.")
        
    def _process_european_qualification(self):
        """Process European competition qualification"""
        for league in self.leagues:
            qualifiers = league.get_european_qualifiers()
            print(f"{league.name} European qualifiers:")
            for comp, clubs in qualifiers.items():
                if clubs:
                    print(f"  {comp}: {[club.name for club in clubs]}")
                    
    def _process_relegation_promotion(self):
        """Process relegation and promotion between leagues"""
        # This would be implemented to handle movement between league tiers
        # For now, just report relegation teams
        for league in self.leagues:
            if league.level == 1:  # Top division
                relegated = league.get_relegation_teams(3)
                print(f"{league.name} relegated teams: {[club.name for club in relegated]}")
                
    def __str__(self) -> str:
        return f"SeasonManager({self.current_season}, {len(self.leagues)} leagues)"
        
    def __repr__(self) -> str:
        return f"SeasonManager(season='{self.current_season}', leagues={len(self.leagues)})"