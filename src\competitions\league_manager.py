"""
Football Manager - League Manager
Manages league competitions, fixtures, tables and scheduling.
"""

from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple
from datetime import datetime, date
import random

from club.club import Club

@dataclass
class LeagueTableEntry:
    club: Club
    position: int
    played: int
    won: int
    drawn: int
    lost: int
    goals_for: int
    goals_against: int
    goal_difference: int
    points: int
    form: str  # Last 5 matches result string (e.g., "WLDWW")

class MatchFixture:
    def __init__(self, home_team: Club, away_team: Club, match_date: date):
        self.home_team = home_team
        self.away_team = away_team
        self.match_date = match_date
        self.home_goals: Optional[int] = None
        self.away_goals: Optional[int] = None
        self.played = False
        self.match_week: Optional[int] = None

class League:
    def __init__(self, league_data: Dict, clubs: List[Club]):
        self.name = league_data['nome_campionato']
        self.country = league_data['paese']
        self.level = league_data['livello_competizione']
        self.season = league_data.get('stagione', '2025/2026')
        
        # Initialize clubs
        self.clubs = clubs
        self.table = self._initialize_table()
        self.fixtures: List[MatchFixture] = []
        self.current_matchday = 0
        self.total_matchdays = 0
        
    def _initialize_table(self) -> List[LeagueTableEntry]:
        """Initialize league table with all clubs"""
        table = []
        for i, club in enumerate(self.clubs):
            entry = LeagueTableEntry(
                club=club,
                position=i+1,
                played=0,
                won=0,
                drawn=0,
                lost=0,
                goals_for=0,
                goals_against=0,
                goal_difference=0,
                points=0,
                form=""
            )
            table.append(entry)
        return table
        
    def generate_fixtures(self) -> List[MatchFixture]:
        """Generate full season fixture list using round-robin algorithm"""
        if len(self.clubs) % 2 != 0:
            # Add a dummy team if odd number of teams
            clubs = self.clubs + [None]
        else:
            clubs = self.clubs
            
        num_clubs = len(clubs)
        num_rounds = num_clubs - 1
        matches_per_round = num_clubs // 2
        
        fixtures = []
        match_date = date(2025, 8, 15)  # Start date for season
        match_week = 1
        
        for round_num in range(num_rounds):
            # Create a copy of clubs for this round
            round_clubs = clubs[:]
            
            # Fix the first club and rotate the rest
            for i in range(matches_per_round):
                home = round_clubs[i]
                away = round_clubs[num_rounds - i]
                
                if home is not None and away is not None:
                    fixture = MatchFixture(home, away, match_date)
                    fixture.match_week = match_week
                    fixtures.append(fixture)
                    
            # Rotate clubs for next round (keeping first club fixed)
            clubs = [clubs[0]] + clubs[-1:] + clubs[1:-1]
            
            # Update date and week for next round
            match_date = date.fromordinal(match_date.toordinal() + 7)
            match_week += 1
            
        # Generate return fixtures (second half of season)
        second_half_start = match_date
        second_half_week = match_week
        
        for fixture in fixtures[:]:
            return_fixture = MatchFixture(
                fixture.away_team, 
                fixture.home_team, 
                second_half_start
            )
            return_fixture.match_week = second_half_week
            fixtures.append(return_fixture)
            
            # Update date and week
            second_half_start = date.fromordinal(second_half_start.toordinal() + 7)
            second_half_week += 1
            
        self.fixtures = fixtures
        self.total_matchdays = match_week * 2
        return fixtures
        
    def get_current_matchday_fixtures(self) -> List[MatchFixture]:
        """Get fixtures for current matchday"""
        return [f for f in self.fixtures if f.match_week == self.current_matchday]
        
    def play_match(self, fixture: MatchFixture, home_goals: int, away_goals: int):
        """Record match result and update league table"""
        fixture.home_goals = home_goals
        fixture.away_goals = away_goals
        fixture.played = True
        
        # Update home team stats
        home_entry = self._get_table_entry(fixture.home_team)
        if home_entry:
            home_entry.played += 1
            home_entry.goals_for += home_goals
            home_entry.goals_against += away_goals
            home_entry.goal_difference = home_entry.goals_for - home_entry.goals_against
            
            if home_goals > away_goals:
                home_entry.won += 1
                home_entry.points += 3
                home_entry.form += "W"
            elif home_goals == away_goals:
                home_entry.drawn += 1
                home_entry.points += 1
                home_entry.form += "D"
            else:
                home_entry.lost += 1
                home_entry.form += "L"
                
            # Keep only last 5 matches in form
            home_entry.form = home_entry.form[-5:]
            
        # Update away team stats
        away_entry = self._get_table_entry(fixture.away_team)
        if away_entry:
            away_entry.played += 1
            away_entry.goals_for += away_goals
            away_entry.goals_against += home_goals
            away_entry.goal_difference = away_entry.goals_for - away_entry.goals_against
            
            if away_goals > home_goals:
                away_entry.won += 1
                away_entry.points += 3
                away_entry.form += "W"
            elif away_goals == home_goals:
                away_entry.drawn += 1
                away_entry.points += 1
                away_entry.form += "D"
            else:
                away_entry.lost += 1
                away_entry.form += "L"
                
            # Keep only last 5 matches in form
            away_entry.form = away_entry.form[-5:]
            
        # Re-sort table
        self._sort_table()
        
    def _get_table_entry(self, club: Club) -> Optional[LeagueTableEntry]:
        """Get table entry for a specific club"""
        for entry in self.table:
            if entry.club == club:
                return entry
        return None
        
    def _sort_table(self):
        """Sort league table by points, goal difference, and goals scored"""
        self.table.sort(key=lambda x: (
            -x.points,  # Most points first
            -x.goal_difference,  # Best goal difference first
            -x.goals_for,  # Most goals scored first
            x.club.name  # Alphabetical as tiebreaker
        ))
        
        # Update positions
        for i, entry in enumerate(self.table):
            entry.position = i + 1
            
    def get_table(self) -> List[LeagueTableEntry]:
        """Get current league table"""
        return self.table
        
    def get_top_teams(self, count: int = 4) -> List[Club]:
        """Get top teams in the league (for European qualification)"""
        return [entry.club for entry in self.table[:count]]
        
    def get_relegation_teams(self, count: int = 3) -> List[Club]:
        """Get teams facing relegation"""
        return [entry.club for entry in self.table[-count:]]
        
    def advance_matchday(self):
        """Advance to next matchday"""
        if self.current_matchday < self.total_matchdays:
            self.current_matchday += 1
            return True
        return False
        
    def is_season_finished(self) -> bool:
        """Check if league season is finished"""
        return self.current_matchday >= self.total_matchdays and all(f.played for f in self.fixtures)
        
    def get_league_winner(self) -> Optional[Club]:
        """Get league winner"""
        if self.is_season_finished():
            return self.table[0].club
        return None
        
    def get_european_qualifiers(self) -> Dict[str, List[Club]]:
        """Get clubs qualified for European competitions"""
        if not self.is_season_finished():
            return {}
            
        qualifiers = {
            "champions_league": [],
            "europa_league": [],
            "conference_league": []
        }
        
        # Champions League qualification (top 4 in top leagues)
        if self.level == 1:
            qualifiers["champions_league"] = self.get_top_teams(4)
            qualifiers["europa_league"] = self.get_top_teams(8)[4:6]
            qualifiers["conference_league"] = self.get_top_teams(8)[6:8]
        # Europa League qualification (top 2 in second tier)
        elif self.level == 2:
            qualifiers["europa_league"] = self.get_top_teams(2)
            qualifiers["conference_league"] = self.get_top_teams(4)[2:4]
        # Conference League qualification (top 2 in third tier)
        else:
            qualifiers["conference_league"] = self.get_top_teams(2)
            
        return qualifiers
        
    def __str__(self) -> str:
        return f"{self.name} ({self.country}) - Season {self.season}"
        
    def __repr__(self) -> str:
        return f"League(name='{self.name}', clubs={len(self.clubs)}, matchdays={self.total_matchdays})"