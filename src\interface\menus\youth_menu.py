"""
Menu Settore Giovanile
Gestisce il settore giovanile e lo sviluppo dei giovani talenti
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List
from .base_menu import BaseMenu
from ..gui_config import COLORS, FONTS, ICONS


class YouthMenu(BaseMenu):
    """Menu per la gestione del settore giovanile"""
    
    def setup_ui(self):
        """Configura l'interfaccia del menu settore giovanile"""
        # Header
        self.create_section_header(self.main_frame, "Settore Giovanile", 
                                 "Sviluppo e gestione giovani talenti")
        
        # Crea le schede per diverse sezioni giovanili
        tabs_config = [
            {'text': 'Panoramica', 'setup_func': self._setup_overview_tab},
            {'text': 'Squadre Giovanili', 'setup_func': self._setup_teams_tab},
            {'text': 'Talenti', 'setup_func': self._setup_talents_tab},
            {'text': 'Reclutamento', 'setup_func': self._setup_recruitment_tab}
        ]
        
        self.notebook = self.create_tabs(self.main_frame, tabs_config)
    
    def _setup_overview_tab(self, parent: tk.Widget):
        """Configura la scheda panoramica"""
        # Informazioni generali
        overview_frame = ttk.LabelFrame(parent, text="Panoramica Settore Giovanile", padding=15)
        overview_frame.pack(fill='x', pady=(0, 10))
        
        # Grid per le informazioni
        overview_grid = ttk.Frame(overview_frame)
        overview_grid.pack(fill='x')
        
        # Colonna sinistra - Strutture
        left_col = ttk.Frame(overview_grid)
        left_col.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_col, text="🏗️ Strutture", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        youth_level = self.club_data['strutture_giovanili']
        recruitment_level = self.club_data['reclutamento_giovanile']
        
        structures_data = [
            ("Livello Strutture", f"{youth_level}/10 ({self.get_condition_text(youth_level)})"),
            ("Livello Reclutamento", f"{recruitment_level}/10 ({self.get_condition_text(recruitment_level)})"),
            ("Campi Dedicati", "3 campi"),
            ("Foresteria", "20 posti"),
            ("Aule Studio", "2 aule"),
            ("Staff Giovanile", "12 membri")
        ]
        
        for label, value in structures_data:
            self.create_info_row(left_col, label, value)
        
        # Colonna destra - Statistiche
        right_col = ttk.Frame(overview_grid)
        right_col.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_col, text="📊 Statistiche", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        stats_data = [
            ("Giovani Totali", "95"),
            ("Età Media", "16.8 anni"),
            ("Nazionalità Diverse", "8"),
            ("Promossi in Prima Squadra", "3"),
            ("Venduti Proficuamente", "7"),
            ("Valore Totale Generato", "€4.2M")
        ]
        
        for label, value in stats_data:
            color = COLORS['success'] if any(x in str(value) for x in ['M', '€']) else None
            self.create_info_row(right_col, label, value, color)
        
        # Performance squadre giovanili
        performance_frame = ttk.LabelFrame(parent, text="Performance Squadre", padding=15)
        performance_frame.pack(fill='x', pady=10)
        
        performance_columns = [
            {'id': 'category', 'text': 'Categoria', 'width': 100},
            {'id': 'league', 'text': 'Campionato', 'width': 120},
            {'id': 'position', 'text': 'Posizione', 'width': 80},
            {'id': 'points', 'text': 'Punti', 'width': 60},
            {'id': 'record', 'text': 'V-P-S', 'width': 80},
            {'id': 'goals', 'text': 'Gol F/S', 'width': 80},
            {'id': 'form', 'text': 'Forma', 'width': 80}
        ]
        
        performance_data = [
            ["Primavera", "Primavera 1", "2°", "45", "14-3-3", "38/18", "VVPVV"],
            ["Under 18", "Nazionale U18", "1°", "52", "17-1-2", "45/12", "VVVVV"],
            ["Under 17", "Regionale U17", "4°", "38", "12-2-6", "32/24", "VPSV"],
            ["Under 16", "Regionale U16", "1°", "48", "15-3-2", "41/15", "VVVPV"],
            ["Under 15", "Provinciale U15", "3°", "35", "11-2-7", "28/22", "SVVP"],
            ["Under 14", "Provinciale U14", "1°", "50", "16-2-2", "48/18", "VVVVV"]
        ]
        
        self.create_data_table(performance_frame, performance_columns, performance_data, height=6)
        
        # Budget e investimenti
        budget_frame = ttk.LabelFrame(parent, text="Budget e Investimenti", padding=15)
        budget_frame.pack(fill='both', expand=True)
        
        # Grid per budget
        budget_grid = ttk.Frame(budget_frame)
        budget_grid.pack(fill='x')
        
        # Colonna sinistra - Costi
        budget_left = ttk.Frame(budget_grid)
        budget_left.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(budget_left, text="💰 Costi Annuali", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        costs_data = [
            ("Stipendi Staff", "€480K"),
            ("Manutenzione Strutture", "€120K"),
            ("Equipaggiamento", "€80K"),
            ("Trasporti e Trasferte", "€60K"),
            ("Formazione e Corsi", "€40K"),
            ("Totale Costi", "€780K")
        ]
        
        for label, value in costs_data:
            color = COLORS['warning'] if 'Totale' in label else None
            self.create_info_row(budget_left, label, value, color)
        
        # Colonna destra - Ricavi
        budget_right = ttk.Frame(budget_grid)
        budget_right.pack(side='right', fill='both', expand=True)
        
        ttk.Label(budget_right, text="📈 Ricavi e ROI", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        revenue_data = [
            ("Vendite Giocatori", "€1.2M"),
            ("Bonus Promozioni", "€150K"),
            ("Contributi Federali", "€80K"),
            ("Sponsorizzazioni", "€50K"),
            ("Totale Ricavi", "€1.48M"),
            ("ROI Settore Giovanile", "+89.7%")
        ]
        
        for label, value in revenue_data:
            color = COLORS['success'] if any(x in str(value) for x in ['+', 'M']) else None
            self.create_info_row(budget_right, label, value, color)
    
    def _setup_teams_tab(self, parent: tk.Widget):
        """Configura la scheda squadre giovanili"""
        # Selezione categoria
        category_frame = ttk.Frame(parent)
        category_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(category_frame, text="Categoria:").pack(side='left', padx=(0, 10))
        category_var = tk.StringVar(value="Primavera")
        category_combo = ttk.Combobox(category_frame, textvariable=category_var,
                                    values=['Primavera', 'Under 18', 'Under 17', 'Under 16', 'Under 15', 'Under 14'],
                                    state='readonly', width=15)
        category_combo.pack(side='left')
        
        # Rosa squadra selezionata
        squad_frame = ttk.LabelFrame(parent, text="Rosa Primavera", padding=15)
        squad_frame.pack(fill='x', pady=10)
        
        squad_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'position', 'text': 'Pos', 'width': 40},
            {'id': 'age', 'text': 'Età', 'width': 40},
            {'id': 'nationality', 'text': 'Nazionalità', 'width': 80},
            {'id': 'potential', 'text': 'Potenziale', 'width': 80},
            {'id': 'current', 'text': 'Attuale', 'width': 60},
            {'id': 'appearances', 'text': 'Pres', 'width': 40},
            {'id': 'goals', 'text': 'Gol', 'width': 40},
            {'id': 'status', 'text': 'Stato', 'width': 80}
        ]
        
        squad_data = [
            ["Marco Giovane", "ATT", "18", "Italia", "Molto Alto", "72", "15", "8", "Titolare"],
            ["Luca Promessa", "CC", "17", "Italia", "Alto", "68", "18", "3", "Titolare"],
            ["Andrea Futuro", "DIF", "19", "Italia", "Alto", "70", "20", "1", "Titolare"],
            ["Simone Talento", "ATT", "16", "Italia", "Molto Alto", "65", "12", "5", "Riserva"],
            ["Giuseppe Stella", "POR", "18", "Italia", "Medio", "69", "10", "0", "Titolare"],
            ["Carlos Junior", "CC", "17", "Brasile", "Alto", "67", "14", "2", "Titolare"],
            ["Ahmed Hassan", "DIF", "18", "Egitto", "Medio", "66", "16", "0", "Titolare"],
            ["Jean Baptiste", "ATT", "16", "Francia", "Alto", "64", "8", "3", "Riserva"]
        ]
        
        squad_tree = self.create_data_table(squad_frame, squad_columns, squad_data, height=8)
        
        # Pulsanti gestione squadra
        squad_buttons = [
            {'text': '⭐ Promuovi in Prima Squadra', 'command': self._promote_to_first_team, 'style': 'Primary.TButton'},
            {'text': '📋 Dettagli Giocatore', 'command': self._youth_player_details, 'style': 'Secondary.TButton'},
            {'text': '🔄 Cambia Categoria', 'command': self._change_category, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(squad_frame, squad_buttons)
        
        # Staff categoria
        staff_frame = ttk.LabelFrame(parent, text="Staff Primavera", padding=15)
        staff_frame.pack(fill='both', expand=True)
        
        staff_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'role', 'text': 'Ruolo', 'width': 120},
            {'id': 'age', 'text': 'Età', 'width': 40},
            {'id': 'experience', 'text': 'Esperienza', 'width': 80},
            {'id': 'rating', 'text': 'Valutazione', 'width': 80},
            {'id': 'specialty', 'text': 'Specialità', 'width': 100}
        ]
        
        staff_data = [
            ["Giovanni Rossi", "Allenatore", "45", "8 anni", "82/100", "Sviluppo Talenti"],
            ["Marco Bianchi", "Vice Allenatore", "38", "5 anni", "75/100", "Tattica"],
            ["Luca Verdi", "Preparatore", "42", "6 anni", "78/100", "Preparazione Fisica"],
            ["Andrea Neri", "All. Portieri", "40", "4 anni", "80/100", "Tecnica Portieri"]
        ]
        
        self.create_data_table(staff_frame, staff_columns, staff_data, height=4)
        
        # Pulsanti staff
        staff_buttons = [
            {'text': '👥 Gestione Staff', 'command': self._manage_youth_staff, 'style': 'Secondary.TButton'},
            {'text': '📊 Valutazione Performance', 'command': self._staff_performance, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(staff_frame, staff_buttons)
    
    def _setup_talents_tab(self, parent: tk.Widget):
        """Configura la scheda talenti"""
        # Top talenti
        talents_frame = ttk.LabelFrame(parent, text="Top Talenti del Settore Giovanile", padding=15)
        talents_frame.pack(fill='x', pady=(0, 10))
        
        talents_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'age', 'text': 'Età', 'width': 40},
            {'id': 'position', 'text': 'Pos', 'width': 40},
            {'id': 'category', 'text': 'Categoria', 'width': 80},
            {'id': 'potential', 'text': 'Potenziale', 'width': 80},
            {'id': 'current', 'text': 'Attuale', 'width': 60},
            {'id': 'development', 'text': 'Sviluppo', 'width': 80},
            {'id': 'interest', 'text': 'Interesse Esterno', 'width': 120}
        ]
        
        talents_data = [
            ["Marco Giovane", "18", "ATT", "Primavera", "Eccezionale", "72", "+8 (6 mesi)", "Milan, Juventus"],
            ["Simone Talento", "16", "ATT", "Under 17", "Molto Alto", "65", "+12 (6 mesi)", "Inter, Roma"],
            ["Luca Promessa", "17", "CC", "Under 18", "Alto", "68", "+6 (6 mesi)", "Atalanta"],
            ["Andrea Futuro", "19", "DIF", "Primavera", "Alto", "70", "+5 (6 mesi)", "Fiorentina"],
            ["Carlos Junior", "17", "CC", "Under 18", "Alto", "67", "+7 (6 mesi)", "Napoli"],
            ["Jean Baptiste", "16", "ATT", "Under 17", "Alto", "64", "+9 (6 mesi)", "Lazio"]
        ]
        
        talents_tree = self.create_data_table(talents_frame, talents_columns, talents_data, height=6)
        
        # Pulsanti talenti
        talents_buttons = [
            {'text': '⭐ Rinnova Contratto', 'command': self._renew_youth_contract, 'style': 'Primary.TButton'},
            {'text': '🛡️ Proteggi Talento', 'command': self._protect_talent, 'style': 'Primary.TButton'},
            {'text': '📈 Piano Sviluppo', 'command': self._development_plan, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(talents_frame, talents_buttons)
        
        # Programmi di sviluppo
        development_frame = ttk.LabelFrame(parent, text="Programmi di Sviluppo", padding=15)
        development_frame.pack(fill='x', pady=10)
        
        development_columns = [
            {'id': 'program', 'text': 'Programma', 'width': 150},
            {'id': 'participants', 'text': 'Partecipanti', 'width': 80},
            {'id': 'focus', 'text': 'Focus', 'width': 120},
            {'id': 'duration', 'text': 'Durata', 'width': 80},
            {'id': 'cost', 'text': 'Costo', 'width': 80},
            {'id': 'effectiveness', 'text': 'Efficacia', 'width': 80}
        ]
        
        development_data = [
            ["Elite Academy", "12", "Sviluppo Completo", "12 mesi", "€50K", "95%"],
            ["Finishing School", "8", "Finalizzazione", "6 mesi", "€20K", "88%"],
            ["Tactical Minds", "15", "Intelligenza Tattica", "9 mesi", "€30K", "82%"],
            ["Physical Prep", "20", "Preparazione Fisica", "12 mesi", "€25K", "90%"],
            ["Mental Coaching", "10", "Aspetto Mentale", "6 mesi", "€15K", "85%"]
        ]
        
        self.create_data_table(development_frame, development_columns, development_data, height=5)
        
        # Pulsanti programmi
        development_buttons = [
            {'text': '➕ Nuovo Programma', 'command': self._new_program, 'style': 'Primary.TButton'},
            {'text': '📊 Valuta Efficacia', 'command': self._evaluate_programs, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(development_frame, development_buttons)
        
        # Statistiche sviluppo
        stats_frame = ttk.LabelFrame(parent, text="Statistiche Sviluppo", padding=15)
        stats_frame.pack(fill='both', expand=True)
        
        # Grid per statistiche
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill='x')
        
        # Colonna sinistra
        left_stats = ttk.Frame(stats_grid)
        left_stats.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_stats, text="📈 Progressi", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        progress_stats = [
            ("Miglioramento Medio", "+6.8 punti/anno"),
            ("Talenti con +10", "8 giocatori"),
            ("Successo Programmi", "87%"),
            ("Soddisfazione Giovani", "92%")
        ]
        
        for label, value in progress_stats:
            color = COLORS['success'] if any(x in str(value) for x in ['+', '%']) else None
            self.create_info_row(left_stats, label, value, color)
        
        # Colonna destra
        right_stats = ttk.Frame(stats_grid)
        right_stats.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_stats, text="🎯 Obiettivi", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        targets_stats = [
            ("Promozioni Target", "4/anno"),
            ("Vendite Proficue", "6/anno"),
            ("Valore Generato", "€3M/anno"),
            ("ROI Investimenti", "+120%")
        ]
        
        for label, value in targets_stats:
            color = COLORS['success'] if any(x in str(value) for x in ['+', 'M']) else None
            self.create_info_row(right_stats, label, value, color)
    
    def _setup_recruitment_tab(self, parent: tk.Widget):
        """Configura la scheda reclutamento"""
        # Scout giovanili
        scouts_frame = ttk.LabelFrame(parent, text="Scout Settore Giovanile", padding=15)
        scouts_frame.pack(fill='x', pady=(0, 10))
        
        scouts_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'region', 'text': 'Area', 'width': 100},
            {'id': 'age_focus', 'text': 'Età Focus', 'width': 80},
            {'id': 'rating', 'text': 'Valutazione', 'width': 80},
            {'id': 'discoveries', 'text': 'Scoperte', 'width': 80},
            {'id': 'success_rate', 'text': 'Successo', 'width': 80}
        ]
        
        scouts_data = [
            ["Mario Rossi", "Lombardia", "14-16", "88/100", "23", "78%"],
            ["Luca Bianchi", "Lazio", "16-18", "85/100", "18", "82%"],
            ["Giuseppe Verdi", "Campania", "15-17", "82/100", "15", "75%"],
            ["Antonio Neri", "Sicilia", "14-18", "80/100", "12", "71%"]
        ]
        
        self.create_data_table(scouts_frame, scouts_columns, scouts_data, height=4)
        
        # Candidati recenti
        candidates_frame = ttk.LabelFrame(parent, text="Candidati Recenti", padding=15)
        candidates_frame.pack(fill='x', pady=10)
        
        candidates_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'age', 'text': 'Età', 'width': 40},
            {'id': 'position', 'text': 'Pos', 'width': 40},
            {'id': 'club', 'text': 'Club Attuale', 'width': 100},
            {'id': 'scout', 'text': 'Scout', 'width': 100},
            {'id': 'potential', 'text': 'Potenziale', 'width': 80},
            {'id': 'cost', 'text': 'Costo', 'width': 80},
            {'id': 'status', 'text': 'Stato', 'width': 80}
        ]
        
        candidates_data = [
            ["Francesco Stella", "15", "ATT", "Roma U16", "M. Rossi", "Alto", "€50K", "In trattativa"],
            ["Matteo Brillante", "16", "CC", "Milan U17", "L. Bianchi", "Molto Alto", "€80K", "Interessato"],
            ["Davide Promettente", "14", "DIF", "Juventus U15", "G. Verdi", "Medio", "€30K", "Valutazione"],
            ["Simone Veloce", "17", "ATT", "Atalanta U18", "A. Neri", "Alto", "€60K", "Rifiutato"]
        ]
        
        candidates_tree = self.create_data_table(candidates_frame, candidates_columns, candidates_data, height=4)
        
        # Pulsanti reclutamento
        recruitment_buttons = [
            {'text': '💰 Fai Offerta', 'command': self._make_youth_offer, 'style': 'Primary.TButton'},
            {'text': '👁️ Osserva Giocatore', 'command': self._scout_youth_player, 'style': 'Secondary.TButton'},
            {'text': '📋 Dettagli Candidato', 'command': self._candidate_details, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(candidates_frame, recruitment_buttons)
        
        # Statistiche reclutamento
        recruitment_stats_frame = ttk.LabelFrame(parent, text="Statistiche Reclutamento", padding=15)
        recruitment_stats_frame.pack(fill='both', expand=True)
        
        # Grid per statistiche reclutamento
        recruitment_grid = ttk.Frame(recruitment_stats_frame)
        recruitment_grid.pack(fill='x')
        
        # Colonna sinistra
        left_recruitment = ttk.Frame(recruitment_grid)
        left_recruitment.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_recruitment, text="🎯 Attività", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        activity_stats = [
            ("Giocatori Osservati", "156"),
            ("Offerte Fatte", "23"),
            ("Acquisizioni", "8"),
            ("Tasso Successo", "35%"),
            ("Budget Utilizzato", "€380K"),
            ("Budget Rimanente", "€120K")
        ]
        
        for label, value in activity_stats:
            color = COLORS['success'] if 'Rimanente' in label else COLORS['warning'] if 'Utilizzato' in label else None
            self.create_info_row(left_recruitment, label, value, color)
        
        # Colonna destra
        right_recruitment = ttk.Frame(recruitment_grid)
        right_recruitment.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_recruitment, text="📊 Risultati", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        results_stats = [
            ("Talenti Acquisiti", "8"),
            ("Valore Medio", "€47.5K"),
            ("Potenziale Medio", "Alto"),
            ("Età Media", "15.8 anni"),
            ("Nazionalità", "6 diverse"),
            ("ROI Previsto", "+280%")
        ]
        
        for label, value in results_stats:
            color = COLORS['success'] if any(x in str(value) for x in ['+', '%', 'Alto']) else None
            self.create_info_row(right_recruitment, label, value, color)
        
        # Pulsanti statistiche
        stats_buttons = [
            {'text': '📈 Report Reclutamento', 'command': self._recruitment_report, 'style': 'Secondary.TButton'},
            {'text': '🎯 Imposta Obiettivi', 'command': self._set_targets, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(recruitment_stats_frame, stats_buttons)
    
    # Metodi placeholder per le azioni
    def _promote_to_first_team(self):
        self.show_message("Promuovi in Prima Squadra", "Funzionalità in sviluppo", "info")
    
    def _youth_player_details(self):
        self.show_message("Dettagli Giocatore", "Funzionalità in sviluppo", "info")
    
    def _change_category(self):
        self.show_message("Cambia Categoria", "Funzionalità in sviluppo", "info")
    
    def _manage_youth_staff(self):
        self.show_message("Gestione Staff", "Funzionalità in sviluppo", "info")
    
    def _staff_performance(self):
        self.show_message("Valutazione Performance", "Funzionalità in sviluppo", "info")
    
    def _renew_youth_contract(self):
        self.show_message("Rinnova Contratto", "Funzionalità in sviluppo", "info")
    
    def _protect_talent(self):
        self.show_message("Proteggi Talento", "Funzionalità in sviluppo", "info")
    
    def _development_plan(self):
        self.show_message("Piano Sviluppo", "Funzionalità in sviluppo", "info")
    
    def _new_program(self):
        self.show_message("Nuovo Programma", "Funzionalità in sviluppo", "info")
    
    def _evaluate_programs(self):
        self.show_message("Valuta Efficacia", "Funzionalità in sviluppo", "info")
    
    def _make_youth_offer(self):
        self.show_message("Fai Offerta", "Funzionalità in sviluppo", "info")
    
    def _scout_youth_player(self):
        self.show_message("Osserva Giocatore", "Funzionalità in sviluppo", "info")
    
    def _candidate_details(self):
        self.show_message("Dettagli Candidato", "Funzionalità in sviluppo", "info")
    
    def _recruitment_report(self):
        self.show_message("Report Reclutamento", "Funzionalità in sviluppo", "info")
    
    def _set_targets(self):
        self.show_message("Imposta Obiettivi", "Funzionalità in sviluppo", "info")
