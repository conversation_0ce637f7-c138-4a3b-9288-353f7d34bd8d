"""
Classe base per tutte le finestre dell'interfaccia grafica
Fornisce funzionalità comuni e stile consistente
"""

import tkinter as tk
from tkinter import ttk, messagebox
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, Callable
from .gui_config import COLORS, FONTS, COMPONENT_SIZES, TEXTS, ICONS


class BaseWindow(ABC):
    """Classe base astratta per tutte le finestre del gioco"""
    
    def __init__(self, parent: Optional[tk.Widget] = None, title: str = "Football Manager"):
        self.parent = parent
        self.title = title
        self.window: Optional[tk.Toplevel] = None
        self.callbacks: Dict[str, Callable] = {}
        
        # Configurazione stile
        self.style = ttk.Style()
        self._configure_styles()
        
    def _configure_styles(self):
        """Configura gli stili personalizzati per i widget"""
        # Stile per i pulsanti principali
        self.style.configure(
            'Primary.TButton',
            background=COLORS['primary'],
            foreground='white',
            font=FONTS['button'],
            padding=(10, 5)
        )
        
        # Stile per i pulsanti secondari
        self.style.configure(
            'Secondary.TButton',
            background=COLORS['secondary'],
            foreground='white',
            font=FONTS['button'],
            padding=(8, 4)
        )
        
        # Stile per le etichette titolo
        self.style.configure(
            'Title.TLabel',
            font=FONTS['title'],
            foreground=COLORS['text_primary'],
            background=COLORS['background']
        )
        
        # Stile per le etichette sottotitolo
        self.style.configure(
            'Subtitle.TLabel',
            font=FONTS['subtitle'],
            foreground=COLORS['text_primary'],
            background=COLORS['background']
        )
        
        # Stile per il frame principale
        self.style.configure(
            'Main.TFrame',
            background=COLORS['background'],
            relief='flat'
        )
        
        # Stile per i frame delle sezioni
        self.style.configure(
            'Section.TFrame',
            background=COLORS['surface'],
            relief='raised',
            borderwidth=1
        )
    
    def create_window(self, width: int = 800, height: int = 600, resizable: bool = True) -> tk.Toplevel:
        """Crea e configura la finestra principale"""
        if self.parent:
            self.window = tk.Toplevel(self.parent)
        else:
            self.window = tk.Tk()
            
        self.window.title(self.title)
        self.window.geometry(f"{width}x{height}")
        
        if not resizable:
            self.window.resizable(False, False)
            
        # Centra la finestra
        self._center_window(width, height)
        
        # Configura il comportamento di chiusura
        self.window.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        return self.window
    
    def _center_window(self, width: int, height: int):
        """Centra la finestra sullo schermo"""
        if not self.window:
            return
            
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_header(self, parent: tk.Widget, title: str, subtitle: str = "") -> ttk.Frame:
        """Crea un header standard per la finestra"""
        header_frame = ttk.Frame(parent, style='Section.TFrame')
        header_frame.pack(fill='x', padx=COMPONENT_SIZES['padding'], pady=COMPONENT_SIZES['margin'])
        
        # Titolo principale
        title_label = ttk.Label(header_frame, text=title, style='Title.TLabel')
        title_label.pack(anchor='w', padx=COMPONENT_SIZES['padding'], pady=(COMPONENT_SIZES['padding'], 0))
        
        # Sottotitolo opzionale
        if subtitle:
            subtitle_label = ttk.Label(header_frame, text=subtitle, style='Subtitle.TLabel')
            subtitle_label.pack(anchor='w', padx=COMPONENT_SIZES['padding'], pady=(0, COMPONENT_SIZES['padding']))
        
        return header_frame
    
    def create_button_frame(self, parent: tk.Widget, buttons: list) -> ttk.Frame:
        """Crea un frame con pulsanti allineati"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill='x', padx=COMPONENT_SIZES['padding'], pady=COMPONENT_SIZES['margin'])
        
        for button_config in buttons:
            text = button_config.get('text', '')
            command = button_config.get('command', None)
            style = button_config.get('style', 'Primary.TButton')
            side = button_config.get('side', 'right')
            
            btn = ttk.Button(
                button_frame,
                text=text,
                command=command,
                style=style
            )
            btn.pack(side=side, padx=COMPONENT_SIZES['margin'])
        
        return button_frame
    
    def show_message(self, message: str, title: str = "Informazione", msg_type: str = "info"):
        """Mostra un messaggio all'utente"""
        if msg_type == "error":
            messagebox.showerror(title, message)
        elif msg_type == "warning":
            messagebox.showwarning(title, message)
        elif msg_type == "question":
            return messagebox.askyesno(title, message)
        else:
            messagebox.showinfo(title, message)
    
    def add_callback(self, event: str, callback: Callable):
        """Aggiunge un callback per un evento specifico"""
        self.callbacks[event] = callback
    
    def trigger_callback(self, event: str, *args, **kwargs):
        """Esegue un callback se presente"""
        if event in self.callbacks:
            return self.callbacks[event](*args, **kwargs)
    
    def _on_closing(self):
        """Gestisce la chiusura della finestra"""
        if self.trigger_callback('on_closing') is not False:
            if self.window:
                self.window.destroy()
    
    @abstractmethod
    def setup_ui(self):
        """Metodo astratto per configurare l'interfaccia utente"""
        pass
    
    @abstractmethod
    def setup_events(self):
        """Metodo astratto per configurare gli eventi"""
        pass
    
    def show(self):
        """Mostra la finestra"""
        if not self.window:
            self.create_window()
        
        self.setup_ui()
        self.setup_events()
        
        if hasattr(self.window, 'mainloop'):
            self.window.mainloop()
    
    def hide(self):
        """Nasconde la finestra"""
        if self.window:
            self.window.withdraw()
    
    def close(self):
        """Chiude la finestra"""
        if self.window:
            self.window.destroy()
            self.window = None


class ModalDialog(BaseWindow):
    """Classe base per finestre di dialogo modali"""
    
    def __init__(self, parent: tk.Widget, title: str = "Dialogo"):
        super().__init__(parent, title)
        self.result = None
    
    def show_modal(self) -> Any:
        """Mostra la finestra come dialogo modale e restituisce il risultato"""
        if not self.window:
            self.create_window(resizable=False)
        
        self.setup_ui()
        self.setup_events()
        
        # Rende la finestra modale
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Attende la chiusura
        self.window.wait_window()
        
        return self.result
    
    def close_with_result(self, result: Any):
        """Chiude la finestra con un risultato"""
        self.result = result
        self.close()
