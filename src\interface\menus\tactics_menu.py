"""
Menu Tattica
Gestisce formazioni, schemi tattici e istruzioni di gioco
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List
from .base_menu import BaseMenu
from ..gui_config import COLORS, FONTS, ICONS


class TacticsMenu(BaseMenu):
    """Menu per la gestione della tattica"""
    
    def __init__(self, parent: tk.Widget, club_data: Dict, game_engine, data_loader):
        super().__init__(parent, club_data, game_engine, data_loader)
        
        # Formazioni disponibili
        self.formations = {
            '4-3-3': {'defenders': 4, 'midfielders': 3, 'forwards': 3},
            '4-4-2': {'defenders': 4, 'midfielders': 4, 'forwards': 2},
            '3-5-2': {'defenders': 3, 'midfielders': 5, 'forwards': 2},
            '4-2-3-1': {'defenders': 4, 'midfielders': 5, 'forwards': 1},
            '3-4-3': {'defenders': 3, 'midfielders': 4, 'forwards': 3},
            '5-3-2': {'defenders': 5, 'midfielders': 3, 'forwards': 2}
        }
        
        self.current_formation = self.club_data.get('formazione_predefinita', '4-3-3')
        self.selected_players = {}
    
    def setup_ui(self):
        """Configura l'interfaccia del menu tattica"""
        # Header
        self.create_section_header(self.main_frame, "Gestione Tattica", 
                                 f"Impostazioni tattiche del {self.club_data['nome']}")
        
        # Crea le schede per diverse sezioni tattiche
        tabs_config = [
            {'text': 'Formazione', 'setup_func': self._setup_formation_tab},
            {'text': 'Istruzioni', 'setup_func': self._setup_instructions_tab},
            {'text': 'Set Pieces', 'setup_func': self._setup_setpieces_tab},
            {'text': 'Analisi', 'setup_func': self._setup_analysis_tab}
        ]
        
        self.notebook = self.create_tabs(self.main_frame, tabs_config)
    
    def _setup_formation_tab(self, parent: tk.Widget):
        """Configura la scheda formazione"""
        # Controlli formazione
        formation_controls = ttk.LabelFrame(parent, text="Selezione Formazione", padding=15)
        formation_controls.pack(fill='x', pady=(0, 10))
        
        # Selezione formazione
        formation_frame = ttk.Frame(formation_controls)
        formation_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(formation_frame, text="Formazione:", font=FONTS['normal']).pack(side='left')
        
        self.formation_var = tk.StringVar(value=self.current_formation)
        formation_combo = ttk.Combobox(formation_frame, textvariable=self.formation_var,
                                     values=list(self.formations.keys()),
                                     state='readonly', width=10)
        formation_combo.pack(side='left', padx=(10, 20))
        formation_combo.bind('<<ComboboxSelected>>', self._on_formation_changed)
        
        # Mentalità
        ttk.Label(formation_frame, text="Mentalità:", font=FONTS['normal']).pack(side='left')
        
        mentality_var = tk.StringVar(value="Equilibrata")
        mentality_combo = ttk.Combobox(formation_frame, textvariable=mentality_var,
                                     values=['Molto Difensiva', 'Difensiva', 'Equilibrata', 'Offensiva', 'Molto Offensiva'],
                                     state='readonly', width=15)
        mentality_combo.pack(side='left', padx=(10, 0))
        
        # Contenitore principale per campo e giocatori
        main_container = ttk.Frame(parent)
        main_container.pack(fill='both', expand=True)
        
        # Campo di gioco (sinistra)
        field_frame = ttk.LabelFrame(main_container, text="Campo di Gioco", padding=10)
        field_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # Canvas per il campo
        self.field_canvas = tk.Canvas(field_frame, bg='#2E7D32', width=400, height=600)
        self.field_canvas.pack(expand=True)
        
        self._draw_field()
        self._draw_formation()
        
        # Lista giocatori (destra)
        players_frame = ttk.LabelFrame(main_container, text="Giocatori Disponibili", padding=10)
        players_frame.pack(side='right', fill='y', padx=(10, 0))
        
        # Filtro per posizione
        filter_frame = ttk.Frame(players_frame)
        filter_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(filter_frame, text="Posizione:").pack(anchor='w')
        position_combo = ttk.Combobox(filter_frame, 
                                    values=['Tutte', 'Portieri', 'Difensori', 'Centrocampisti', 'Attaccanti'],
                                    state='readonly', width=15)
        position_combo.set('Tutte')
        position_combo.pack(fill='x')
        
        # Lista giocatori
        players_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'position', 'text': 'Pos', 'width': 40},
            {'id': 'rating', 'text': 'Val', 'width': 40}
        ]
        
        players_data = [
            ["M. Rossi", "ATT", "85"],
            ["L. Bianchi", "CC", "82"],
            ["G. Verdi", "DIF", "80"],
            ["A. Neri", "POR", "78"],
            ["F. Blu", "ATT", "77"],
            ["C. Silva", "CC", "83"],
            ["J. Dupont", "DIF", "79"],
            ["H. Mueller", "CC", "76"],
            ["P. Garcia", "ATT", "74"],
            ["A. Gialli", "DIF", "75"]
        ]
        
        self.players_tree = self.create_data_table(players_frame, players_columns, players_data, height=15)
        
        # Pulsanti controllo
        formation_buttons = [
            {'text': '💾 Salva Formazione', 'command': self._save_formation, 'style': 'Primary.TButton'},
            {'text': '🔄 Reset', 'command': self._reset_formation, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(formation_controls, formation_buttons)
    
    def _setup_instructions_tab(self, parent: tk.Widget):
        """Configura la scheda istruzioni tattiche"""
        # Istruzioni di squadra
        team_instructions = ttk.LabelFrame(parent, text="Istruzioni di Squadra", padding=15)
        team_instructions.pack(fill='x', pady=(0, 10))
        
        # Grid per le istruzioni
        instructions_grid = ttk.Frame(team_instructions)
        instructions_grid.pack(fill='x')
        
        # Colonna sinistra
        left_instructions = ttk.Frame(instructions_grid)
        left_instructions.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_instructions, text="Fase Offensiva", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        offensive_options = [
            ("Stile di Gioco", ['Possesso', 'Contropiede', 'Diretto', 'Misto']),
            ("Ritmo", ['Lento', 'Normale', 'Veloce']),
            ("Ampiezza", ['Stretta', 'Normale', 'Ampia']),
            ("Pressing", ['Basso', 'Medio', 'Alto', 'Molto Alto'])
        ]
        
        for label, options in offensive_options:
            option_frame = ttk.Frame(left_instructions)
            option_frame.pack(fill='x', pady=5)
            
            ttk.Label(option_frame, text=f"{label}:", width=12).pack(side='left')
            combo = ttk.Combobox(option_frame, values=options, state='readonly', width=12)
            combo.set(options[1])  # Valore di default
            combo.pack(side='right')
        
        # Colonna destra
        right_instructions = ttk.Frame(instructions_grid)
        right_instructions.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_instructions, text="Fase Difensiva", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        defensive_options = [
            ("Linea Difensiva", ['Molto Bassa', 'Bassa', 'Normale', 'Alta', 'Molto Alta']),
            ("Marcatura", ['Zonale', 'A Uomo', 'Mista']),
            ("Aggressività", ['Bassa', 'Normale', 'Alta']),
            ("Fuorigioco", ['Raramente', 'A volte', 'Spesso'])
        ]
        
        for label, options in defensive_options:
            option_frame = ttk.Frame(right_instructions)
            option_frame.pack(fill='x', pady=5)
            
            ttk.Label(option_frame, text=f"{label}:", width=12).pack(side='left')
            combo = ttk.Combobox(option_frame, values=options, state='readonly', width=12)
            combo.set(options[2])  # Valore di default
            combo.pack(side='right')
        
        # Istruzioni individuali
        individual_instructions = ttk.LabelFrame(parent, text="Istruzioni Individuali", padding=15)
        individual_instructions.pack(fill='both', expand=True)
        
        # Tabella istruzioni giocatori
        individual_columns = [
            {'id': 'player', 'text': 'Giocatore', 'width': 120},
            {'id': 'position', 'text': 'Posizione', 'width': 80},
            {'id': 'role', 'text': 'Ruolo', 'width': 100},
            {'id': 'instructions', 'text': 'Istruzioni', 'width': 200}
        ]
        
        individual_data = [
            ["M. Rossi", "ATT", "Punta Centrale", "Resta in area, Cerca il gol"],
            ["L. Bianchi", "CC", "Regista", "Gioca semplice, Distribuisci palla"],
            ["G. Verdi", "DIF", "Difensore Centrale", "Marca stretto, Anticipa"],
            ["A. Neri", "POR", "Portiere", "Distribuzione corta, Resta sulla linea"]
        ]
        
        self.create_data_table(individual_instructions, individual_columns, individual_data, height=8)
    
    def _setup_setpieces_tab(self, parent: tk.Widget):
        """Configura la scheda calci piazzati"""
        # Calci d'angolo
        corners_frame = ttk.LabelFrame(parent, text="Calci d'Angolo", padding=15)
        corners_frame.pack(fill='x', pady=(0, 10))
        
        corners_grid = ttk.Frame(corners_frame)
        corners_grid.pack(fill='x')
        
        # Angoli offensivi
        off_corners = ttk.Frame(corners_grid)
        off_corners.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(off_corners, text="Offensivi", font=FONTS['subtitle']).pack(anchor='w')
        
        corner_options = [
            ("Battitore", "L. Bianchi"),
            ("Schema", "Corto"),
            ("Giocatori in Area", "5")
        ]
        
        for label, value in corner_options:
            self.create_info_row(off_corners, label, value)
        
        # Angoli difensivi
        def_corners = ttk.Frame(corners_grid)
        def_corners.pack(side='right', fill='both', expand=True)
        
        ttk.Label(def_corners, text="Difensivi", font=FONTS['subtitle']).pack(anchor='w')
        
        def_corner_options = [
            ("Marcatura", "Zonale"),
            ("Uomo sul Primo Palo", "G. Verdi"),
            ("Uomo sul Secondo Palo", "A. Gialli")
        ]
        
        for label, value in def_corner_options:
            self.create_info_row(def_corners, label, value)
        
        # Punizioni
        freekicks_frame = ttk.LabelFrame(parent, text="Punizioni", padding=15)
        freekicks_frame.pack(fill='x', pady=10)
        
        freekicks_grid = ttk.Frame(freekicks_frame)
        freekicks_grid.pack(fill='x')
        
        # Punizioni offensive
        off_freekicks = ttk.Frame(freekicks_grid)
        off_freekicks.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(off_freekicks, text="Offensive", font=FONTS['subtitle']).pack(anchor='w')
        
        freekick_options = [
            ("Battitore Destro", "M. Rossi"),
            ("Battitore Sinistro", "L. Bianchi"),
            ("Seconda Opzione", "P. Garcia")
        ]
        
        for label, value in freekick_options:
            self.create_info_row(off_freekicks, label, value)
        
        # Punizioni difensive
        def_freekicks = ttk.Frame(freekicks_grid)
        def_freekicks.pack(side='right', fill='both', expand=True)
        
        ttk.Label(def_freekicks, text="Difensive", font=FONTS['subtitle']).pack(anchor='w')
        
        def_freekick_options = [
            ("Barriera", "3-4 giocatori"),
            ("Marcatura", "A zona"),
            ("Contropiede", "Attivo")
        ]
        
        for label, value in def_freekick_options:
            self.create_info_row(def_freekicks, label, value)
        
        # Rigori
        penalties_frame = ttk.LabelFrame(parent, text="Rigori", padding=15)
        penalties_frame.pack(fill='both', expand=True)
        
        penalty_takers = [
            ("1° Rigorista", "M. Rossi", "92%"),
            ("2° Rigorista", "L. Bianchi", "87%"),
            ("3° Rigorista", "P. Garcia", "83%")
        ]
        
        for position, player, percentage in penalty_takers:
            penalty_frame = ttk.Frame(penalties_frame)
            penalty_frame.pack(fill='x', pady=2)
            
            ttk.Label(penalty_frame, text=f"{position}:", width=15).pack(side='left')
            ttk.Label(penalty_frame, text=player, width=15).pack(side='left')
            ttk.Label(penalty_frame, text=f"({percentage})", 
                     foreground=COLORS['text_secondary']).pack(side='left')
    
    def _setup_analysis_tab(self, parent: tk.Widget):
        """Configura la scheda analisi tattica"""
        # Statistiche tattiche
        stats_frame = ttk.LabelFrame(parent, text="Statistiche Tattiche", padding=15)
        stats_frame.pack(fill='x', pady=(0, 10))
        
        tactical_stats = [
            ("Possesso Palla Medio", "58%", COLORS['success']),
            ("Passaggi Completati", "84%", COLORS['success']),
            ("Duelli Aerei Vinti", "67%", COLORS['info']),
            ("Contrasti Riusciti", "71%", COLORS['info']),
            ("Tiri in Porta", "4.2/partita", COLORS['warning']),
            ("Gol Subiti", "1.1/partita", COLORS['error'])
        ]
        
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill='x')
        
        for i, (label, value, color) in enumerate(tactical_stats):
            col = i % 3
            row = i // 3
            
            stat_frame = ttk.Frame(stats_grid)
            stat_frame.grid(row=row, column=col, sticky='ew', padx=10, pady=5)
            stats_grid.columnconfigure(col, weight=1)
            
            self.create_info_row(stat_frame, label, value, color)
        
        # Efficacia formazioni
        formations_analysis = ttk.LabelFrame(parent, text="Efficacia Formazioni", padding=15)
        formations_analysis.pack(fill='x', pady=10)
        
        formations_columns = [
            {'id': 'formation', 'text': 'Formazione', 'width': 80},
            {'id': 'matches', 'text': 'Partite', 'width': 60},
            {'id': 'wins', 'text': 'Vittorie', 'width': 60},
            {'id': 'draws', 'text': 'Pareggi', 'width': 60},
            {'id': 'losses', 'text': 'Sconfitte', 'width': 70},
            {'id': 'goals_for', 'text': 'Gol Fatti', 'width': 70},
            {'id': 'goals_against', 'text': 'Gol Subiti', 'width': 70},
            {'id': 'points_avg', 'text': 'Punti/P', 'width': 60}
        ]
        
        formations_data = [
            ["4-3-3", "12", "7", "3", "2", "18", "8", "2.0"],
            ["4-4-2", "6", "2", "2", "2", "7", "6", "1.3"],
            ["3-5-2", "4", "3", "1", "0", "8", "2", "2.5"],
            ["4-2-3-1", "3", "1", "1", "1", "4", "3", "1.3"]
        ]
        
        self.create_data_table(formations_analysis, formations_columns, formations_data, height=5)
        
        # Raccomandazioni
        recommendations_frame = ttk.LabelFrame(parent, text="Raccomandazioni Tattiche", padding=15)
        recommendations_frame.pack(fill='both', expand=True)
        
        recommendations = [
            ("✅", "La formazione 3-5-2 mostra ottimi risultati", COLORS['success']),
            ("⚠️", "Migliorare la fase difensiva sui calci piazzati", COLORS['warning']),
            ("ℹ️", "Considerare un approccio più offensivo in casa", COLORS['info']),
            ("⚠️", "Troppi gol subiti da situazioni di contropiede", COLORS['warning'])
        ]
        
        for icon, message, color in recommendations:
            rec_frame = ttk.Frame(recommendations_frame)
            rec_frame.pack(fill='x', pady=3)
            
            ttk.Label(rec_frame, text=icon, font=FONTS['normal']).pack(side='left', padx=(0, 10))
            ttk.Label(rec_frame, text=message, font=FONTS['normal'], 
                     foreground=color).pack(side='left')
    
    def _draw_field(self):
        """Disegna il campo di calcio"""
        canvas = self.field_canvas
        
        # Sfondo campo
        canvas.create_rectangle(20, 20, 380, 580, fill='#2E7D32', outline='white', width=2)
        
        # Linea di metà campo
        canvas.create_line(20, 300, 380, 300, fill='white', width=2)
        
        # Cerchio di centrocampo
        canvas.create_oval(170, 250, 230, 350, outline='white', width=2)
        
        # Aree di rigore
        canvas.create_rectangle(80, 20, 320, 100, outline='white', width=2)  # Area superiore
        canvas.create_rectangle(80, 500, 320, 580, outline='white', width=2)  # Area inferiore
        
        # Aree piccole
        canvas.create_rectangle(140, 20, 260, 60, outline='white', width=2)  # Piccola superiore
        canvas.create_rectangle(140, 540, 260, 580, outline='white', width=2)  # Piccola inferiore
        
        # Punti di rigore
        canvas.create_oval(198, 68, 202, 72, fill='white')  # Rigore superiore
        canvas.create_oval(198, 528, 202, 532, fill='white')  # Rigore inferiore
    
    def _draw_formation(self):
        """Disegna la formazione sul campo"""
        formation = self.formations[self.current_formation]
        
        # Posizioni base per la formazione
        positions = self._calculate_positions(formation)
        
        # Disegna i giocatori
        for i, (x, y) in enumerate(positions):
            # Cerchio giocatore
            canvas_x = 20 + (x * 340)
            canvas_y = 580 - (y * 540)  # Inverti Y per avere la porta in basso
            
            self.field_canvas.create_oval(canvas_x-15, canvas_y-15, canvas_x+15, canvas_y+15,
                                        fill='blue', outline='white', width=2)
            
            # Numero giocatore
            self.field_canvas.create_text(canvas_x, canvas_y, text=str(i+1), 
                                        fill='white', font=('Arial', 10, 'bold'))
    
    def _calculate_positions(self, formation: Dict) -> List[tuple]:
        """Calcola le posizioni dei giocatori per la formazione"""
        positions = []
        
        # Portiere
        positions.append((0.5, 0.05))
        
        # Difensori
        def_count = formation['defenders']
        for i in range(def_count):
            x = 0.2 + (i * 0.6 / (def_count - 1)) if def_count > 1 else 0.5
            positions.append((x, 0.25))
        
        # Centrocampisti
        mid_count = formation['midfielders']
        for i in range(mid_count):
            x = 0.15 + (i * 0.7 / (mid_count - 1)) if mid_count > 1 else 0.5
            positions.append((x, 0.55))
        
        # Attaccanti
        att_count = formation['forwards']
        for i in range(att_count):
            x = 0.25 + (i * 0.5 / (att_count - 1)) if att_count > 1 else 0.5
            positions.append((x, 0.8))
        
        return positions
    
    def _on_formation_changed(self, event=None):
        """Gestisce il cambio di formazione"""
        self.current_formation = self.formation_var.get()
        self.field_canvas.delete("all")
        self._draw_field()
        self._draw_formation()
    
    def _save_formation(self):
        """Salva la formazione corrente"""
        self.show_message("Salva Formazione", f"Formazione {self.current_formation} salvata con successo!", "info")
    
    def _reset_formation(self):
        """Reset della formazione"""
        self.current_formation = self.club_data.get('formazione_predefinita', '4-3-3')
        self.formation_var.set(self.current_formation)
        self._on_formation_changed()
