"""
Football Manager - Match Engine
Simulates football matches and generates realistic results.
"""

from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple
import random
from datetime import datetime

from club.club import Club
from players.player import Player, Position

@dataclass
class MatchEvent:
    minute: int
    event_type: str  # 'goal', 'yellow_card', 'red_card', 'substitution', 'injury'
    player: Optional[Player] = None
    team: Optional[Club] = None
    description: str = ""
    additional_data: Dict = None

@dataclass
class MatchResult:
    home_goals: int
    away_goals: int
    home_club: Club
    away_club: Club
    events: List[MatchEvent]
    home_possession: int  # Percentage
    away_possession: int  # Percentage
    home_shots: int
    away_shots: int
    home_shots_on_target: int
    away_shots_on_target: int
    match_date: datetime

class MatchEngine:
    def __init__(self):
        self.home_advantage_factor = 1.1  # Home advantage multiplier
        
    def simulate_match(self, home_team: Club, away_team: Club, 
                      home_formation: str = None, away_formation: str = None) -> MatchResult:
        """Simulate a football match between two teams"""
        
        # Calculate team strengths
        home_strength = self._calculate_team_strength(home_team, home_formation)
        away_strength = self._calculate_team_strength(away_team, away_formation)
        
        # Apply home advantage
        home_strength *= self.home_advantage_factor
        
        # Calculate expected goals based on strength difference
        strength_ratio = home_strength / (home_strength + away_strength)
        expected_goals = 2.5 + random.uniform(-0.5, 0.5)  # Average goals per match
        
        home_expected_goals = expected_goals * strength_ratio * 1.1  # Slightly more goals for home
        away_expected_goals = expected_goals * (1 - strength_ratio)
        
        # Generate actual goals using Poisson distribution approximation
        home_goals = self._poisson_distribution(home_expected_goals)
        away_goals = self._poisson_distribution(away_expected_goals)
        
        # Generate match statistics
        home_possession = 50 + int((strength_ratio - 0.5) * 20) + random.randint(-5, 5)
        away_possession = 100 - home_possession
        
        # Shots based on possession and strength
        total_shots = int(expected_goals * 3.5) + random.randint(-3, 3)
        home_shots = int(total_shots * (home_possession / 100)) + random.randint(-2, 2)
        away_shots = total_shots - home_shots
        
        # Shots on target (roughly 30-40% of total shots)
        home_shots_on_target = int(home_shots * (0.3 + random.uniform(-0.1, 0.1)))
        away_shots_on_target = int(away_shots * (0.3 + random.uniform(-0.1, 0.1)))
        
        # Ensure shots on target don't exceed total shots
        home_shots_on_target = min(home_shots_on_target, home_shots)
        away_shots_on_target = min(away_shots_on_target, away_shots)
        
        # Generate match events
        events = self._generate_match_events(home_team, away_team, home_goals, away_goals)
        
        # Create match result
        result = MatchResult(
            home_goals=home_goals,
            away_goals=away_goals,
            home_club=home_team,
            away_club=away_team,
            events=events,
            home_possession=home_possession,
            away_possession=away_possession,
            home_shots=home_shots,
            away_shots=away_shots,
            home_shots_on_target=home_shots_on_target,
            away_shots_on_target=away_shots_on_target,
            match_date=datetime.now()
        )
        
        return result
        
    def _calculate_team_strength(self, team: Club, formation: str = None) -> float:
        """Calculate team strength based on player ratings and other factors"""
        if not team.players:
            return 50.0  # Default strength for teams with no players
            
        # Calculate average player rating
        total_rating = sum(player.calculate_overall_rating() for player in team.players)
        avg_rating = total_rating / len(team.players)
        
        # Apply formation bonus (if formation is optimal for team)
        formation_bonus = 1.0
        if formation and formation == team.default_formation:
            formation_bonus = 1.05  # 5% bonus for playing preferred formation
            
        # Apply reputation factor (better teams perform better)
        reputation_factor = 1.0 + (team.reputation.overall - 10) / 100
        
        # Apply fitness factor (average team fitness)
        avg_fitness = sum(player.fitness for player in team.players) / len(team.players)
        fitness_factor = avg_fitness / 100  # Fitness is 1-100 scale
        
        # Combine all factors
        strength = avg_rating * formation_bonus * reputation_factor * fitness_factor
        
        return strength
        
    def _poisson_distribution(self, expected_value: float) -> int:
        """Generate random number using Poisson distribution"""
        # Simple approximation using inverse transform method
        L = pow(2.718281828, -expected_value)
        k = 0
        p = 1.0
        
        while p > L:
            k += 1
            p *= random.random()
            
        return k - 1
        
    def _generate_match_events(self, home_team: Club, away_team: Club, 
                             home_goals: int, away_goals: int) -> List[MatchEvent]:
        """Generate match events like goals, cards, substitutions"""
        events = []
        
        # Generate goals
        for i in range(home_goals):
            minute = random.randint(1, 90)
            # Select a random attacker from home team
            attackers = [p for p in home_team.players if p.position in [Position.ST, Position.W, Position.AM]]
            if attackers:
                player = random.choice(attackers)
                event = MatchEvent(
                    minute=minute,
                    event_type='goal',
                    player=player,
                    team=home_team,
                    description=f"{player.full_name} scores for {home_team.name}"
                )
                events.append(event)
                
        for i in range(away_goals):
            minute = random.randint(1, 90)
            # Select a random attacker from away team
            attackers = [p for p in away_team.players if p.position in [Position.ST, Position.W, Position.AM]]
            if attackers:
                player = random.choice(attackers)
                event = MatchEvent(
                    minute=minute,
                    event_type='goal',
                    player=player,
                    team=away_team,
                    description=f"{player.full_name} scores for {away_team.name}"
                )
                events.append(event)
                
        # Generate yellow cards (typically 2-5 per match)
        yellow_cards = random.randint(2, 5)
        for i in range(yellow_cards):
            minute = random.randint(1, 90)
            team = random.choice([home_team, away_team])
            # Select a random player from team
            if team.players:
                player = random.choice(team.players)
                event = MatchEvent(
                    minute=minute,
                    event_type='yellow_card',
                    player=player,
                    team=team,
                    description=f"{player.full_name} receives a yellow card"
                )
                events.append(event)
                
        # Generate red cards (typically 0-1 per match)
        if random.random() < 0.1:  # 10% chance of red card
            minute = random.randint(1, 90)
            team = random.choice([home_team, away_team])
            # Select a random player from team
            if team.players:
                player = random.choice(team.players)
                event = MatchEvent(
                    minute=minute,
                    event_type='red_card',
                    player=player,
                    team=team,
                    description=f"{player.full_name} receives a red card"
                )
                events.append(event)
                
        # Generate substitutions (typically 0-3 per team)
        for team in [home_team, away_team]:
            subs = random.randint(0, 3)
            for i in range(subs):
                minute = random.randint(60, 90)  # Most subs happen after 60th minute
                # Select a random player from team
                if team.players:
                    player_out = random.choice(team.players)
                    event = MatchEvent(
                        minute=minute,
                        event_type='substitution',
                        player=player_out,
                        team=team,
                        description=f"Substitution for {team.name}"
                    )
                    events.append(event)
                    
        # Sort events by minute
        events.sort(key=lambda x: x.minute)
        
        return events
        
    def get_match_result_string(self, result: MatchResult) -> str:
        """Get a string representation of the match result"""
        return f"{result.home_club.name} {result.home_goals} - {result.away_goals} {result.away_club.name}"
        
    def get_match_winner(self, result: MatchResult) -> Optional[Club]:
        """Get the winning team or None for a draw"""
        if result.home_goals > result.away_goals:
            return result.home_club
        elif result.away_goals > result.home_goals:
            return result.away_club
        else:
            return None  # Draw
            
    def get_match_loser(self, result: MatchResult) -> Optional[Club]:
        """Get the losing team or None for a draw"""
        if result.home_goals > result.away_goals:
            return result.away_club
        elif result.away_goals > result.home_goals:
            return result.home_club
        else:
            return None  # Draw