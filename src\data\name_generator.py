"""
Football Manager - Name Generator
Generates realistic names for players, staff and other personnel based on nationality.
"""

import random
from typing import List, Dict, Tu<PERSON>, Optional
from .data_loader import DataLoader

class NameGenerator:
    def __init__(self, data_loader: DataLoader):
        self.data_loader = data_loader
        self.used_names = set()
        
        # Common European coaching surnames for realism
        self.coaching_surnames = {
            '<PERSON>': ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],
            'Germania': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>lick', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'],
            '<PERSON><PERSON>': ['<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>'],
            '<PERSON><PERSON><PERSON>': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>'],
            'Inghilt<PERSON>ra': ['<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],
            '<PERSON><PERSON><PERSON>': ['<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>'],
            '<PERSON><PERSON><PERSON>': ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ge']
        }
    
    def generate_player_name(self, nationality: str, avoid_duplicates: bool = True) -> Tuple[str, str]:
        """Generate realistic player name for given nationality"""
        name_data = self.data_loader.get_names_for_country(nationality)
        
        max_attempts = 50
        attempts = 0
        
        while attempts < max_attempts:
            first_name = random.choice(name_data['first_names'])
            last_name = random.choice(name_data['last_names'])
            full_name = f"{first_name} {last_name}"
            
            if not avoid_duplicates or full_name not in self.used_names:
                if avoid_duplicates:
                    self.used_names.add(full_name)
                return first_name, last_name
            
            attempts += 1
        
        # Fallback if we can't find unique name
        first_name = random.choice(name_data['first_names'])
        last_name = f"{random.choice(name_data['last_names'])}{random.randint(1, 999)}"
        return first_name, last_name
    
    def generate_coach_name(self, nationality: str) -> Tuple[str, str]:
        """Generate realistic coach name, preferring known coaching surnames"""
        name_data = self.data_loader.get_names_for_country(nationality)
        
        first_name = random.choice(name_data['first_names'])
        
        # 30% chance to use famous coaching surname if available
        if nationality in self.coaching_surnames and random.random() < 0.3:
            last_name = random.choice(self.coaching_surnames[nationality])
        else:
            last_name = random.choice(name_data['last_names'])
        
        return first_name, last_name
    
    def generate_staff_name(self, nationality: str, role: str = "generic") -> Tuple[str, str]:
        """Generate staff member name based on role"""
        name_data = self.data_loader.get_names_for_country(nationality)
        
        # For medical staff, prefer more formal/professional sounding names
        if role in ['doctor', 'physiotherapist']:
            first_names = [name for name in name_data['first_names'] 
                          if len(name) > 4]  # Longer names sound more professional
            if first_names:
                first_name = random.choice(first_names)
            else:
                first_name = random.choice(name_data['first_names'])
        else:
            first_name = random.choice(name_data['first_names'])
        
        last_name = random.choice(name_data['last_names'])
        return first_name, last_name
    
    def generate_agent_name(self, nationality: str) -> Tuple[str, str]:
        """Generate agent name (tends to be more international/sophisticated)"""
        name_data = self.data_loader.get_names_for_country(nationality)
        
        # Agents often have more international/business-like names
        first_name = random.choice(name_data['first_names'])
        last_name = random.choice(name_data['last_names'])
        
        return first_name, last_name
    
    def generate_board_member_name(self, nationality: str) -> Tuple[str, str]:
        """Generate board member name (more formal/business-oriented)"""
        name_data = self.data_loader.get_names_for_country(nationality)
        
        # Board members tend to have traditional, formal names
        traditional_names = [name for name in name_data['first_names'] 
                           if len(name) >= 5]  # Longer, more formal names
        
        if traditional_names:
            first_name = random.choice(traditional_names)
        else:
            first_name = random.choice(name_data['first_names'])
        
        last_name = random.choice(name_data['last_names'])
        return first_name, last_name
    
    def generate_youth_player_name(self, nationality: str) -> Tuple[str, str]:
        """Generate youth player name (modern naming trends)"""
        name_data = self.data_loader.get_names_for_country(nationality)
        
        # Youth players might have more modern/shorter names
        modern_names = [name for name in name_data['first_names'] 
                       if len(name) <= 6]  # Shorter, more modern names
        
        if modern_names:
            first_name = random.choice(modern_names)
        else:
            first_name = random.choice(name_data['first_names'])
        
        last_name = random.choice(name_data['last_names'])
        return first_name, last_name
    
    def generate_nickname(self, first_name: str, last_name: str, nationality: str) -> Optional[str]:
        """Generate nickname for player (optional)"""
        # Brazilian players often use single names or nicknames
        if nationality == 'Brasile' and random.random() < 0.4:
            nickname_options = [
                first_name,  # Just first name
                last_name,   # Just last name
                first_name[:3] + "inho",  # Diminutive
                last_name[:3] + "inho",   # Diminutive
                first_name + "ão",       # Augmentative
            ]
            return random.choice(nickname_options)
        
        # Other nationalities rarely use nicknames in this context
        if random.random() < 0.05:  # 5% chance
            return first_name[:4] if len(first_name) > 4 else None
        
        return None
    
    def get_name_statistics(self) -> Dict[str, int]:
        """Get statistics about generated names"""
        return {
            'total_generated': len(self.used_names),
            'available_countries': len(self.data_loader.player_names),
            'coaching_countries': len(self.coaching_surnames)
        }
    
    def reset_used_names(self):
        """Reset the used names set (useful for new game)"""
        self.used_names.clear()
    
    def is_name_available(self, first_name: str, last_name: str) -> bool:
        """Check if a name combination is available"""
        full_name = f"{first_name} {last_name}"
        return full_name not in self.used_names
    
    def reserve_name(self, first_name: str, last_name: str):
        """Reserve a name to prevent duplicates"""
        full_name = f"{first_name} {last_name}"
        self.used_names.add(full_name)