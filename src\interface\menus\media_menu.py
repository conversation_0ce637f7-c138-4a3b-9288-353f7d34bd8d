"""
Menu Media e Comunicazione
Gestisce conferenze stampa, interviste e relazioni con i media
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List
from .base_menu import BaseMenu
from ..gui_config import COLORS, FONTS, ICONS


class MediaMenu(BaseMenu):
    """Menu per la gestione dei media e comunicazione"""
    
    def setup_ui(self):
        """Configura l'interfaccia del menu media"""
        # Header
        self.create_section_header(self.main_frame, "Media e Comunicazione", 
                                 "Conferenze stampa, interviste e relazioni pubbliche")
        
        # Crea le schede per diverse sezioni media
        tabs_config = [
            {'text': 'Conferenze Stampa', 'setup_func': self._setup_press_tab},
            {'text': 'Interviste', 'setup_func': self._setup_interviews_tab},
            {'text': 'Social Media', 'setup_func': self._setup_social_tab},
            {'text': 'Reputazione', 'setup_func': self._setup_reputation_tab}
        ]
        
        self.notebook = self.create_tabs(self.main_frame, tabs_config)
    
    def _setup_press_tab(self, parent: tk.Widget):
        """Configura la scheda conferenze stampa"""
        # Prossime conferenze
        upcoming_frame = ttk.LabelFrame(parent, text="Prossime Conferenze Stampa", padding=15)
        upcoming_frame.pack(fill='x', pady=(0, 10))
        
        upcoming_columns = [
            {'id': 'date', 'text': 'Data', 'width': 100},
            {'id': 'time', 'text': 'Ora', 'width': 60},
            {'id': 'type', 'text': 'Tipo', 'width': 120},
            {'id': 'topic', 'text': 'Argomento', 'width': 150},
            {'id': 'location', 'text': 'Luogo', 'width': 100},
            {'id': 'status', 'text': 'Stato', 'width': 80}
        ]
        
        upcoming_data = [
            ["20/01/2025", "15:00", "Pre-partita", "vs Juventus", "Centro Sportivo", "Programmata"],
            ["22/01/2025", "17:30", "Post-partita", "vs Juventus", "Stadio", "Automatica"],
            ["25/01/2025", "11:00", "Mercato", "Nuovi Acquisti", "Sala Stampa", "Richiesta"],
            ["28/01/2025", "14:00", "Pre-partita", "vs Milan", "Centro Sportivo", "Programmata"]
        ]
        
        upcoming_tree = self.create_data_table(upcoming_frame, upcoming_columns, upcoming_data, height=4)
        
        # Pulsanti conferenze
        press_buttons = [
            {'text': '📅 Programma Conferenza', 'command': self._schedule_press, 'style': 'Primary.TButton'},
            {'text': '✏️ Prepara Dichiarazioni', 'command': self._prepare_statements, 'style': 'Secondary.TButton'},
            {'text': '❌ Annulla Conferenza', 'command': self._cancel_press, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(upcoming_frame, press_buttons)
        
        # Storico conferenze recenti
        recent_frame = ttk.LabelFrame(parent, text="Conferenze Recenti", padding=15)
        recent_frame.pack(fill='x', pady=10)
        
        recent_columns = [
            {'id': 'date', 'text': 'Data', 'width': 80},
            {'id': 'type', 'text': 'Tipo', 'width': 100},
            {'id': 'topic', 'text': 'Argomento', 'width': 120},
            {'id': 'tone', 'text': 'Tono', 'width': 80},
            {'id': 'media_reaction', 'text': 'Reazione Media', 'width': 100},
            {'id': 'impact', 'text': 'Impatto', 'width': 80}
        ]
        
        recent_data = [
            ["15/01", "Post-partita", "Vittoria vs Roma", "Positivo", "Molto Positiva", "+3 Morale"],
            ["08/01", "Pre-partita", "Preparazione Derby", "Cauto", "Neutrale", "Neutro"],
            ["22/12", "Mercato", "Obiettivi Gennaio", "Ambizioso", "Positiva", "+2 Aspettative"],
            ["15/12", "Post-partita", "Sconfitta vs Inter", "Deluso", "Critica", "-1 Fiducia"],
            ["08/12", "Settimanale", "Situazione Squadra", "Fiducioso", "Positiva", "+1 Morale"]
        ]
        
        self.create_data_table(recent_frame, recent_columns, recent_data, height=5)
        
        # Statistiche conferenze stampa
        stats_frame = ttk.LabelFrame(parent, text="Statistiche Conferenze Stampa", padding=15)
        stats_frame.pack(fill='both', expand=True)
        
        # Grid per statistiche
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill='x')
        
        # Colonna sinistra
        left_stats = ttk.Frame(stats_grid)
        left_stats.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_stats, text="📊 Attività", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        activity_stats = [
            ("Conferenze Totali", "45"),
            ("Media Mensile", "3.2"),
            ("Tono Positivo", "67%"),
            ("Tono Neutro", "22%"),
            ("Tono Negativo", "11%"),
            ("Partecipazione Media", "85%")
        ]
        
        for label, value in activity_stats:
            color = COLORS['success'] if 'Positivo' in label else COLORS['warning'] if 'Negativo' in label else None
            self.create_info_row(left_stats, label, value, color)
        
        # Colonna destra
        right_stats = ttk.Frame(stats_grid)
        right_stats.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_stats, text="📈 Impatto", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        impact_stats = [
            ("Morale Squadra", "+8%"),
            ("Fiducia Tifosi", "+12%"),
            ("Aspettative", "Moderate"),
            ("Pressione Media", "Media"),
            ("Reputazione", "+5 punti"),
            ("Credibilità", "Alta")
        ]
        
        for label, value in impact_stats:
            color = COLORS['success'] if '+' in str(value) or 'Alta' in str(value) else None
            self.create_info_row(right_stats, label, value, color)
    
    def _setup_interviews_tab(self, parent: tk.Widget):
        """Configura la scheda interviste"""
        # Richieste di intervista
        requests_frame = ttk.LabelFrame(parent, text="Richieste di Intervista", padding=15)
        requests_frame.pack(fill='x', pady=(0, 10))
        
        requests_columns = [
            {'id': 'media', 'text': 'Media', 'width': 120},
            {'id': 'journalist', 'text': 'Giornalista', 'width': 100},
            {'id': 'topic', 'text': 'Argomento', 'width': 150},
            {'id': 'format', 'text': 'Formato', 'width': 80},
            {'id': 'deadline', 'text': 'Scadenza', 'width': 80},
            {'id': 'priority', 'text': 'Priorità', 'width': 80}
        ]
        
        requests_data = [
            ["Gazzetta dello Sport", "M. Rossi", "Obiettivi Stagionali", "Scritta", "25/01", "Alta"],
            ["Sky Sport", "L. Bianchi", "Mercato Gennaio", "TV", "22/01", "Media"],
            ["Corriere dello Sport", "G. Verdi", "Situazione Squadra", "Scritta", "28/01", "Bassa"],
            ["DAZN", "A. Neri", "Analisi Tattica", "Video", "30/01", "Media"],
            ["Radio RAI", "F. Blu", "Rapporto con Tifosi", "Radio", "26/01", "Bassa"]
        ]
        
        requests_tree = self.create_data_table(requests_frame, requests_columns, requests_data, height=5)
        
        # Pulsanti interviste
        interview_buttons = [
            {'text': '✅ Accetta Intervista', 'command': self._accept_interview, 'style': 'Primary.TButton'},
            {'text': '❌ Rifiuta Intervista', 'command': self._decline_interview, 'style': 'Secondary.TButton'},
            {'text': '⏰ Rimanda Intervista', 'command': self._postpone_interview, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(requests_frame, interview_buttons)
        
        # Interviste programmate
        scheduled_frame = ttk.LabelFrame(parent, text="Interviste Programmate", padding=15)
        scheduled_frame.pack(fill='x', pady=10)
        
        scheduled_columns = [
            {'id': 'date', 'text': 'Data', 'width': 80},
            {'id': 'time', 'text': 'Ora', 'width': 60},
            {'id': 'media', 'text': 'Media', 'width': 120},
            {'id': 'format', 'text': 'Formato', 'width': 80},
            {'id': 'duration', 'text': 'Durata', 'width': 60},
            {'id': 'location', 'text': 'Luogo', 'width': 100},
            {'id': 'preparation', 'text': 'Preparazione', 'width': 80}
        ]
        
        scheduled_data = [
            ["21/01", "10:00", "Gazzetta", "Scritta", "30min", "Ufficio", "Completata"],
            ["23/01", "15:30", "Sky Sport", "TV", "15min", "Centro Sportivo", "In corso"],
            ["26/01", "11:00", "DAZN", "Video", "20min", "Stadio", "Da iniziare"]
        ]
        
        self.create_data_table(scheduled_frame, scheduled_columns, scheduled_data, height=3)
        
        # Relazioni con i giornalisti
        relations_frame = ttk.LabelFrame(parent, text="Relazioni con i Giornalisti", padding=15)
        relations_frame.pack(fill='both', expand=True)
        
        relations_columns = [
            {'id': 'journalist', 'text': 'Giornalista', 'width': 120},
            {'id': 'media', 'text': 'Testata', 'width': 120},
            {'id': 'relationship', 'text': 'Rapporto', 'width': 80},
            {'id': 'interviews', 'text': 'Interviste', 'width': 60},
            {'id': 'tone', 'text': 'Tono Medio', 'width': 80},
            {'id': 'influence', 'text': 'Influenza', 'width': 80}
        ]
        
        relations_data = [
            ["Marco Rossi", "Gazzetta", "Buono", "12", "Positivo", "Alta"],
            ["Luca Bianchi", "Sky Sport", "Ottimo", "8", "Molto Positivo", "Molto Alta"],
            ["Giuseppe Verdi", "Corriere", "Neutro", "5", "Neutro", "Media"],
            ["Anna Neri", "DAZN", "Buono", "6", "Positivo", "Alta"],
            ["Francesco Blu", "Radio RAI", "Difficile", "3", "Critico", "Media"]
        ]
        
        relations_tree = self.create_data_table(relations_frame, relations_columns, relations_data, height=5)
        
        # Pulsanti relazioni
        relations_buttons = [
            {'text': '🤝 Migliora Rapporto', 'command': self._improve_relationship, 'style': 'Primary.TButton'},
            {'text': '📊 Analisi Influenza', 'command': self._analyze_influence, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(relations_frame, relations_buttons)
    
    def _setup_social_tab(self, parent: tk.Widget):
        """Configura la scheda social media"""
        # Panoramica social
        overview_frame = ttk.LabelFrame(parent, text="Panoramica Social Media", padding=15)
        overview_frame.pack(fill='x', pady=(0, 10))
        
        # Grid per panoramica
        overview_grid = ttk.Frame(overview_frame)
        overview_grid.pack(fill='x')
        
        # Colonna sinistra - Follower
        left_overview = ttk.Frame(overview_grid)
        left_overview.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_overview, text="👥 Follower", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        followers_data = [
            ("Twitter", "2.3M (+12K)"),
            ("Instagram", "1.8M (+8K)"),
            ("Facebook", "3.1M (+5K)"),
            ("TikTok", "850K (+15K)"),
            ("YouTube", "420K (+2K)"),
            ("Totale", "8.47M (+42K)")
        ]
        
        for label, value in followers_data:
            color = COLORS['success'] if '+' in str(value) else None
            self.create_info_row(left_overview, label, value, color)
        
        # Colonna destra - Engagement
        right_overview = ttk.Frame(overview_grid)
        right_overview.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_overview, text="📊 Engagement", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        engagement_data = [
            ("Like Medi", "45K per post"),
            ("Commenti Medi", "2.8K per post"),
            ("Condivisioni", "1.2K per post"),
            ("Tasso Engagement", "4.2%"),
            ("Reach Settimanale", "12.5M"),
            ("Sentiment", "78% Positivo")
        ]
        
        for label, value in engagement_data:
            color = COLORS['success'] if any(x in str(value) for x in ['%', 'Positivo']) else None
            self.create_info_row(right_overview, label, value, color)
        
        # Post recenti
        posts_frame = ttk.LabelFrame(parent, text="Post Recenti", padding=15)
        posts_frame.pack(fill='x', pady=10)
        
        posts_columns = [
            {'id': 'date', 'text': 'Data', 'width': 80},
            {'id': 'platform', 'text': 'Piattaforma', 'width': 80},
            {'id': 'content', 'text': 'Contenuto', 'width': 200},
            {'id': 'likes', 'text': 'Like', 'width': 60},
            {'id': 'comments', 'text': 'Commenti', 'width': 70},
            {'id': 'shares', 'text': 'Condivisioni', 'width': 80}
        ]
        
        posts_data = [
            ["15/01", "Instagram", "Vittoria vs Roma - Foto squadra", "52K", "3.2K", "1.8K"],
            ["14/01", "Twitter", "Preparazione alla prossima partita", "28K", "1.5K", "800"],
            ["13/01", "Facebook", "Intervista al capitano", "35K", "2.1K", "1.2K"],
            ["12/01", "TikTok", "Allenamento - Momenti divertenti", "78K", "4.5K", "2.3K"],
            ["11/01", "YouTube", "Highlights partita precedente", "125K", "8.2K", "3.1K"]
        ]
        
        self.create_data_table(posts_frame, posts_columns, posts_data, height=5)
        
        # Pulsanti social
        social_buttons = [
            {'text': '📝 Nuovo Post', 'command': self._new_post, 'style': 'Primary.TButton'},
            {'text': '📅 Programma Contenuti', 'command': self._schedule_content, 'style': 'Secondary.TButton'},
            {'text': '📊 Analisi Performance', 'command': self._social_analytics, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(posts_frame, social_buttons)
        
        # Campagne social
        campaigns_frame = ttk.LabelFrame(parent, text="Campagne Social", padding=15)
        campaigns_frame.pack(fill='both', expand=True)
        
        campaigns_columns = [
            {'id': 'campaign', 'text': 'Campagna', 'width': 150},
            {'id': 'platform', 'text': 'Piattaforme', 'width': 100},
            {'id': 'start_date', 'text': 'Inizio', 'width': 80},
            {'id': 'end_date', 'text': 'Fine', 'width': 80},
            {'id': 'budget', 'text': 'Budget', 'width': 80},
            {'id': 'reach', 'text': 'Reach', 'width': 80},
            {'id': 'status', 'text': 'Stato', 'width': 80}
        ]
        
        campaigns_data = [
            ["#ForzaNapoli2025", "Tutte", "01/01", "31/01", "€15K", "8.2M", "Attiva"],
            ["Nuovi Acquisti", "Instagram, Twitter", "15/01", "31/01", "€8K", "3.5M", "Attiva"],
            ["Match Day Experience", "TikTok, YouTube", "10/01", "28/02", "€12K", "5.1M", "Pianificata"]
        ]
        
        self.create_data_table(campaigns_frame, campaigns_columns, campaigns_data, height=3)
        
        # Pulsanti campagne
        campaign_buttons = [
            {'text': '🚀 Nuova Campagna', 'command': self._new_campaign, 'style': 'Primary.TButton'},
            {'text': '⏸️ Pausa Campagna', 'command': self._pause_campaign, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(campaigns_frame, campaign_buttons)
    
    def _setup_reputation_tab(self, parent: tk.Widget):
        """Configura la scheda reputazione"""
        # Reputazione attuale
        current_frame = ttk.LabelFrame(parent, text="Reputazione Attuale", padding=15)
        current_frame.pack(fill='x', pady=(0, 10))
        
        # Grid per reputazione
        reputation_grid = ttk.Frame(current_frame)
        reputation_grid.pack(fill='x')
        
        # Colonna sinistra - Punteggi
        left_reputation = ttk.Frame(reputation_grid)
        left_reputation.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_reputation, text="⭐ Punteggi Reputazione", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        reputation_scores = [
            ("Reputazione Generale", "82/100", COLORS['success']),
            ("Credibilità Media", "78/100", COLORS['success']),
            ("Popolarità Tifosi", "89/100", COLORS['success']),
            ("Rispetto Avversari", "75/100", COLORS['info']),
            ("Immagine Pubblica", "80/100", COLORS['success']),
            ("Professionalità", "85/100", COLORS['success'])
        ]
        
        for label, value, color in reputation_scores:
            self.create_info_row(left_reputation, label, value, color)
        
        # Colonna destra - Trend
        right_reputation = ttk.Frame(reputation_grid)
        right_reputation.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_reputation, text="📈 Trend (30 giorni)", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        trend_data = [
            ("Variazione Generale", "+5 punti"),
            ("Media Tradizionali", "+3 punti"),
            ("Social Media", "+8 punti"),
            ("Tifosi", "+2 punti"),
            ("Settore", "+4 punti"),
            ("Internazionale", "+1 punto")
        ]
        
        for label, value in trend_data:
            color = COLORS['success'] if '+' in str(value) else COLORS['warning'] if '-' in str(value) else None
            self.create_info_row(right_reputation, label, value, color)
        
        # Fattori di influenza
        factors_frame = ttk.LabelFrame(parent, text="Fattori di Influenza", padding=15)
        factors_frame.pack(fill='x', pady=10)
        
        factors_columns = [
            {'id': 'factor', 'text': 'Fattore', 'width': 150},
            {'id': 'current_impact', 'text': 'Impatto Attuale', 'width': 100},
            {'id': 'trend', 'text': 'Trend', 'width': 80},
            {'id': 'importance', 'text': 'Importanza', 'width': 80},
            {'id': 'controllability', 'text': 'Controllo', 'width': 80}
        ]
        
        factors_data = [
            ["Risultati Sportivi", "Molto Positivo", "↗️", "Molto Alta", "Medio"],
            ["Comportamento Pubblico", "Positivo", "→", "Alta", "Alto"],
            ["Relazioni Media", "Positivo", "↗️", "Alta", "Alto"],
            ["Gestione Crisi", "Neutro", "→", "Media", "Alto"],
            ["Trasparenza", "Positivo", "↗️", "Media", "Alto"],
            ["Carisma Personale", "Molto Positivo", "→", "Alta", "Basso"]
        ]
        
        self.create_data_table(factors_frame, factors_columns, factors_data, height=6)
        
        # Azioni per migliorare reputazione
        actions_frame = ttk.LabelFrame(parent, text="Azioni per Migliorare la Reputazione", padding=15)
        actions_frame.pack(fill='both', expand=True)
        
        actions_columns = [
            {'id': 'action', 'text': 'Azione', 'width': 180},
            {'id': 'impact', 'text': 'Impatto Previsto', 'width': 100},
            {'id': 'cost', 'text': 'Costo', 'width': 80},
            {'id': 'time', 'text': 'Tempo', 'width': 80},
            {'id': 'difficulty', 'text': 'Difficoltà', 'width': 80}
        ]
        
        actions_data = [
            ["Corso Comunicazione", "+8 Credibilità", "€5K", "2 settimane", "Bassa"],
            ["Charity Event", "+5 Immagine", "€15K", "1 mese", "Media"],
            ["Intervista Esclusiva", "+3 Media", "Gratis", "1 giorno", "Bassa"],
            ["Presenza Social Attiva", "+6 Popolarità", "€2K/mese", "Continuo", "Bassa"],
            ["Libro Autobiografico", "+10 Generale", "€20K", "6 mesi", "Alta"]
        ]
        
        actions_tree = self.create_data_table(actions_frame, actions_columns, actions_data, height=5)
        
        # Pulsanti reputazione
        reputation_buttons = [
            {'text': '🚀 Avvia Azione', 'command': self._start_reputation_action, 'style': 'Primary.TButton'},
            {'text': '📊 Analisi Dettagliata', 'command': self._detailed_analysis, 'style': 'Secondary.TButton'},
            {'text': '🎯 Piano Miglioramento', 'command': self._improvement_plan, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(actions_frame, reputation_buttons)
    
    # Metodi placeholder per le azioni
    def _schedule_press(self):
        self.show_message("Programma Conferenza", "Funzionalità in sviluppo", "info")
    
    def _prepare_statements(self):
        self.show_message("Prepara Dichiarazioni", "Funzionalità in sviluppo", "info")
    
    def _cancel_press(self):
        self.show_message("Annulla Conferenza", "Funzionalità in sviluppo", "info")
    
    def _accept_interview(self):
        self.show_message("Accetta Intervista", "Funzionalità in sviluppo", "info")
    
    def _decline_interview(self):
        self.show_message("Rifiuta Intervista", "Funzionalità in sviluppo", "info")
    
    def _postpone_interview(self):
        self.show_message("Rimanda Intervista", "Funzionalità in sviluppo", "info")
    
    def _improve_relationship(self):
        self.show_message("Migliora Rapporto", "Funzionalità in sviluppo", "info")
    
    def _analyze_influence(self):
        self.show_message("Analisi Influenza", "Funzionalità in sviluppo", "info")
    
    def _new_post(self):
        self.show_message("Nuovo Post", "Funzionalità in sviluppo", "info")
    
    def _schedule_content(self):
        self.show_message("Programma Contenuti", "Funzionalità in sviluppo", "info")
    
    def _social_analytics(self):
        self.show_message("Analisi Performance", "Funzionalità in sviluppo", "info")
    
    def _new_campaign(self):
        self.show_message("Nuova Campagna", "Funzionalità in sviluppo", "info")
    
    def _pause_campaign(self):
        self.show_message("Pausa Campagna", "Funzionalità in sviluppo", "info")
    
    def _start_reputation_action(self):
        self.show_message("Avvia Azione", "Funzionalità in sviluppo", "info")
    
    def _detailed_analysis(self):
        self.show_message("Analisi Dettagliata", "Funzionalità in sviluppo", "info")
    
    def _improvement_plan(self):
        self.show_message("Piano Miglioramento", "Funzionalità in sviluppo", "info")
