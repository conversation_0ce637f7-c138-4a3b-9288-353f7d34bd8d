"""
Football Manager - Coaching Staff
Classes for coaching staff including head coaches and their attributes.
"""

from dataclasses import dataclass
from typing import List, Dict, Optional
from datetime import date
from enum import Enum

class CoachingRole(Enum):
    HEAD_COACH = "head_coach"
    ASSISTANT = "assistant"
    GOALKEEPING = "goalkeeping"
    FITNESS = "fitness"
    SCOUT = "scout"
    CHIEF_SCOUT = "chief_scout"
    MEDICAL = "medical"
    PHYSIOTHERAPIST = "physiotherapist"
    PSYCHOLOGIST = "psychologist"
    NUTRITIONIST = "nutritionist"
    TECHNICAL_DIRECTOR = "technical_director"
    YOUTH_COORDINATOR = "youth_coordinator"

class PreferredFormation(Enum):
    FOUR_FOUR_TWO = "4-4-2"
    FOUR_THREE_THREE = "4-3-3"
    THREE_FIVE_TWO = "3-5-2"
    FOUR_TWO_THREE_ONE = "4-2-3-1"
    THREE_FOUR_THREE = "3-4-3"
    FOUR_FIVE_ONE = "4-5-1"
    FIVE_THREE_TWO = "5-3-2"
    FIVE_FOUR_ONE = "5-4-1"

@dataclass
class CoachContract:
    start_date: date
    end_date: date
    salary: int  # Annual salary
    bonuses: Dict[str, int]  # Performance bonuses

class Coach:
    def __init__(self, first_name: str, last_name: str, nationality: str, age: int):
        # Basic information
        self.first_name = first_name
        self.last_name = last_name
        self.full_name = f"{first_name} {last_name}"
        self.nationality = nationality
        self.age = age
        
        # Coaching attributes (1-20 scale)
        self.tactical_knowledge = 10
        self.player_development = 10
        self.man_management = 10
        self.motivation = 10
        self.adaptability = 10
        self.youth_development = 10
        
        # Role and preferences
        self.role = CoachingRole.HEAD_COACH
        self.preferred_formation = PreferredFormation.FOUR_FOUR_TWO
        self.reputation = 1  # 1-20 scale
        
        # Career data
        self.experience_years = 0
        self.previous_clubs = []
        self.trophies_won = {}
        self.contract: Optional[CoachContract] = None
        
        # Current status
        self.current_club = None
        self.morale = 50  # 1-100 scale
        
    def calculate_coaching_rating(self) -> int:
        """Calculate overall coaching rating based on attributes"""
        if self.role == CoachingRole.HEAD_COACH:
            weights = {
                'tactical_knowledge': 0.25,
                'player_development': 0.20,
                'man_management': 0.20,
                'motivation': 0.15,
                'adaptability': 0.10,
                'youth_development': 0.10
            }
        else:
            # For assistant coaches, weight their specialty higher
            weights = {
                'tactical_knowledge': 0.20,
                'player_development': 0.15,
                'man_management': 0.15,
                'motivation': 0.15,
                'adaptability': 0.10,
                'youth_development': 0.25
            }
            
        rating = (
            self.tactical_knowledge * weights['tactical_knowledge'] +
            self.player_development * weights['player_development'] +
            self.man_management * weights['man_management'] +
            self.motivation * weights['motivation'] +
            self.adaptability * weights['adaptability'] +
            self.youth_development * weights['youth_development']
        )
        
        return int(rating)
        
    def add_trophy(self, competition: str, year: int):
        """Add trophy to coach's history"""
        if competition not in self.trophies_won:
            self.trophies_won[competition] = []
        self.trophies_won[competition].append(year)
        
    def get_trophy_count(self, competition: str = None) -> int:
        """Get trophy count for specific competition or total"""
        if competition:
            return len(self.trophies_won.get(competition, []))
        return sum(len(trophies) for trophies in self.trophies_won.values())
        
    def update_morale(self, change: int):
        """Update coach morale"""
        self.morale = max(1, min(100, self.morale + change))
        
    def can_renew_contract(self) -> bool:
        """Check if coach is eligible for contract renewal based on performance"""
        # Simple logic: coaches with higher reputation and trophies are more likely to get renewals
        return self.reputation >= 10 or self.get_trophy_count() > 0
        
    def __str__(self) -> str:
        return f"{self.full_name} ({self.nationality}) - {self.role.value}"
        
    def __repr__(self) -> str:
        return f"Coach(name='{self.full_name}', role='{self.role.value}', reputation={self.reputation})"