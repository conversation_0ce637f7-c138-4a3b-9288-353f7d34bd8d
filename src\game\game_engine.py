"""
Football Manager - Game Engine
Main engine that coordinates all game systems and manages the game loop.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

class GamePhase(Enum):
    PRE_SEASON = "pre_season"
    REGULAR_SEASON = "regular_season" 
    WINTER_BREAK = "winter_break"
    SEASON_END = "season_end"

class GameSpeed(Enum):
    PAUSED = 0
    SLOW = 1
    NORMAL = 2
    FAST = 3

@dataclass
class GameState:
    current_date: datetime
    season: str
    game_phase: GamePhase
    speed: GameSpeed
    day_count: int
    is_running: bool

class GameEngine:
    def __init__(self):
        self.state = GameState(
            current_date=datetime(2025, 7, 1),
            season="2025/2026", 
            game_phase=GamePhase.PRE_SEASON,
            speed=GameSpeed.PAUSED,
            day_count=0,
            is_running=False
        )
        self.player_club = None
        self.all_clubs = {}
        self.competitions = {}
        self.events_queue = []
        
    def initialize_game(self, player_club_name: str):
        """Initialize a new game with player's chosen club"""
        self.player_club = player_club_name
        self.state.is_running = True
        self._setup_season()
        
    def _setup_season(self):
        """Setup new season - competitions, calendars, objectives"""
        self._determine_season_phase()
        self._initialize_competitions()
        self._set_club_objectives()
        
    def _determine_season_phase(self):
        """Determine current phase based on date"""
        month = self.state.current_date.month
        day = self.state.current_date.day
        
        if month == 7 or (month == 8 and day < 15):
            self.state.game_phase = GamePhase.PRE_SEASON
        elif month >= 12 and day > 20 or month == 1 and day < 10:
            self.state.game_phase = GamePhase.WINTER_BREAK
        elif month >= 5 and day > 20:
            self.state.game_phase = GamePhase.SEASON_END
        else:
            self.state.game_phase = GamePhase.REGULAR_SEASON
            
    def advance_day(self):
        """Advance game by one day"""
        if not self.state.is_running:
            return
            
        self.state.current_date += timedelta(days=1)
        self.state.day_count += 1
        
        self._process_daily_events()
        self._check_phase_changes()
        
    def _process_daily_events(self):
        """Process all events for current day"""
        current_events = [e for e in self.events_queue 
                         if e.date.date() == self.state.current_date.date()]
        
        for event in current_events:
            self._execute_event(event)
            self.events_queue.remove(event)
            
    def _execute_event(self, event):
        """Execute a scheduled event"""
        if event.type == "match":
            self._process_match(event)
        elif event.type == "transfer_deadline":
            self._process_transfer_deadline()
        elif event.type == "board_meeting":
            self._process_board_meeting()
            
    def _check_phase_changes(self):
        """Check if game phase should change"""
        old_phase = self.state.game_phase
        self._determine_season_phase()
        
        if old_phase != self.state.game_phase:
            self._handle_phase_change(old_phase, self.state.game_phase)
            
    def _handle_phase_change(self, old_phase: GamePhase, new_phase: GamePhase):
        """Handle transition between game phases"""
        if new_phase == GamePhase.REGULAR_SEASON:
            self._start_regular_season()
        elif new_phase == GamePhase.SEASON_END:
            self._end_season()
            
    def get_current_status(self) -> Dict:
        """Get current game status for UI"""
        return {
            "date": self.state.current_date.strftime("%d/%m/%Y"),
            "season": self.state.season,
            "phase": self.state.game_phase.value,
            "day": self.state.day_count,
            "club": self.player_club,
            "running": self.state.is_running
        }
        
    def pause_game(self):
        """Pause the game"""
        self.state.speed = GameSpeed.PAUSED
        
    def set_speed(self, speed: GameSpeed):
        """Set game speed"""
        self.state.speed = speed
        
    def save_game(self, filename: str) -> bool:
        """Save current game state"""
        try:
            # Implementation will be in save_manager.py
            return True
        except Exception:
            return False
            
    def load_game(self, filename: str) -> bool:
        """Load game state"""
        try:
            # Implementation will be in load_manager.py
            return True
        except Exception:
            return False