"""
Dashboard principale del gioco
Interfaccia principale con menu laterale e area contenuto modulare
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Optional, Any
from datetime import datetime
from .base_window import BaseWindow
from .gui_config import COLORS, FONTS, COMPONENT_SIZES, TEXTS, ICONS
from data.data_loader import DataLoader
from game.game_engine import GameEngine
from .menus.team_menu import TeamMenu
from .menus.finances_menu import FinancesMenu
from .menus.calendar_menu import CalendarMenu
from .menus.tactics_menu import TacticsMenu
from .menus.transfers_menu import TransfersMenu
from .menus.statistics_menu import StatisticsMenu
from .menus.facilities_menu import FacilitiesMenu
from .menus.staff_menu import StaffMenu
from .menus.youth_menu import YouthMenu
from .menus.media_menu import MediaMenu
from .menus.board_menu import BoardMenu


class GameDashboard(BaseWindow):
    """Dashboard principale del gioco"""
    
    def __init__(self, parent: tk.Widget, selected_club: Dict, 
                 game_engine: GameEngine, data_loader: DataLoader):
        super().__init__(parent, f"{TEXTS['app_title']} - {selected_club['nome']}")
        
        self.selected_club = selected_club
        self.game_engine = game_engine
        self.data_loader = data_loader
        
        # Widget references
        self.sidebar = None
        self.content_area = None
        self.header_frame = None
        self.current_content = None
        
        # Menu items configuration
        self.menu_items = [
            {'id': 'home', 'text': TEXTS['menu_items']['home'], 'icon': ICONS['home']},
            {'id': 'team', 'text': TEXTS['menu_items']['team'], 'icon': ICONS['team']},
            {'id': 'tactics', 'text': TEXTS['menu_items']['tactics'], 'icon': ICONS['tactics']},
            {'id': 'transfers', 'text': TEXTS['menu_items']['transfers'], 'icon': ICONS['transfers']},
            {'id': 'finances', 'text': TEXTS['menu_items']['finances'], 'icon': ICONS['finances']},
            {'id': 'facilities', 'text': TEXTS['menu_items']['facilities'], 'icon': ICONS['facilities']},
            {'id': 'staff', 'text': TEXTS['menu_items']['staff'], 'icon': ICONS['staff']},
            {'id': 'youth', 'text': TEXTS['menu_items']['youth'], 'icon': ICONS['youth']},
            {'id': 'calendar', 'text': TEXTS['menu_items']['calendar'], 'icon': ICONS['calendar']},
            {'id': 'statistics', 'text': TEXTS['menu_items']['statistics'], 'icon': ICONS['statistics']},
            {'id': 'media', 'text': TEXTS['menu_items']['media'], 'icon': ICONS['media']},
            {'id': 'board', 'text': TEXTS['menu_items']['board'], 'icon': ICONS['board']}
        ]
        
        self.active_menu = 'home'
    
    def setup_ui(self):
        """Configura l'interfaccia utente della dashboard"""
        if not self.window:
            return
        
        # Frame principale
        main_frame = ttk.Frame(self.window, style='Main.TFrame')
        main_frame.pack(fill='both', expand=True)
        
        # Crea le sezioni principali
        self._create_header(main_frame)
        self._create_main_content(main_frame)
    
    def _create_header(self, parent: tk.Widget):
        """Crea l'header con informazioni del club e controlli"""
        self.header_frame = ttk.Frame(parent, style='Section.TFrame', height=60)
        self.header_frame.pack(fill='x', padx=5, pady=5)
        self.header_frame.pack_propagate(False)
        
        # Informazioni club (sinistra)
        club_info_frame = ttk.Frame(self.header_frame)
        club_info_frame.pack(side='left', fill='y', padx=10)
        
        # Nome club
        club_name_label = ttk.Label(club_info_frame, 
                                  text=self.selected_club['nome'],
                                  font=FONTS['title'],
                                  foreground=COLORS['primary'])
        club_name_label.pack(anchor='w')
        
        # Informazioni aggiuntive
        info_text = f"{self.selected_club['citta']} | {self.selected_club['campionato']}"
        info_label = ttk.Label(club_info_frame, 
                             text=info_text,
                             font=FONTS['small'],
                             foreground=COLORS['text_secondary'])
        info_label.pack(anchor='w')
        
        # Data e controlli gioco (centro)
        game_controls_frame = ttk.Frame(self.header_frame)
        game_controls_frame.pack(side='left', expand=True, fill='y', padx=20)
        
        # Data corrente
        current_date = self.game_engine.state.current_date if self.game_engine else datetime.now()
        date_label = ttk.Label(game_controls_frame,
                             text=current_date.strftime("%d/%m/%Y"),
                             font=FONTS['subtitle'])
        date_label.pack()
        
        # Stagione
        season_text = f"Stagione {self.game_engine.state.season}" if self.game_engine else "2025/2026"
        season_label = ttk.Label(game_controls_frame,
                               text=season_text,
                               font=FONTS['small'],
                               foreground=COLORS['text_secondary'])
        season_label.pack()
        
        # Controlli utente (destra)
        user_controls_frame = ttk.Frame(self.header_frame)
        user_controls_frame.pack(side='right', fill='y', padx=10)
        
        # Pulsanti di controllo
        controls_buttons = [
            {'text': f"{ICONS['save']} Salva", 'command': self._on_save_game},
            {'text': f"{ICONS['settings']} Opzioni", 'command': self._on_settings},
            {'text': f"{ICONS['exit']} Menu", 'command': self._on_back_to_menu}
        ]
        
        for button_config in controls_buttons:
            btn = ttk.Button(user_controls_frame,
                           text=button_config['text'],
                           command=button_config['command'],
                           style='Secondary.TButton')
            btn.pack(side='right', padx=2)
    
    def _create_main_content(self, parent: tk.Widget):
        """Crea l'area principale con sidebar e contenuto"""
        content_frame = ttk.Frame(parent)
        content_frame.pack(fill='both', expand=True, padx=5, pady=(0, 5))
        
        # Sidebar (menu laterale)
        self._create_sidebar(content_frame)
        
        # Area contenuto principale
        self._create_content_area(content_frame)
        
        # Carica il contenuto iniziale (Home)
        self._load_content('home')
    
    def _create_sidebar(self, parent: tk.Widget):
        """Crea la sidebar con il menu di navigazione"""
        self.sidebar = ttk.Frame(parent, style='Section.TFrame', width=COMPONENT_SIZES['menu_width'])
        self.sidebar.pack(side='left', fill='y', padx=(0, 5))
        self.sidebar.pack_propagate(False)
        
        # Titolo sidebar
        sidebar_title = ttk.Label(self.sidebar, 
                                text="Menu Principale",
                                font=FONTS['subtitle'],
                                foreground=COLORS['text_primary'])
        sidebar_title.pack(pady=10)
        
        # Separatore
        ttk.Separator(self.sidebar, orient='horizontal').pack(fill='x', padx=10, pady=5)
        
        # Menu items
        self.menu_buttons = {}
        for item in self.menu_items:
            self._create_menu_button(item)
    
    def _create_menu_button(self, menu_item: Dict):
        """Crea un pulsante del menu"""
        button_frame = ttk.Frame(self.sidebar)
        button_frame.pack(fill='x', padx=10, pady=2)
        
        # Stile del pulsante basato sullo stato attivo
        style = 'Primary.TButton' if menu_item['id'] == self.active_menu else 'Secondary.TButton'
        
        button_text = f"{menu_item['icon']} {menu_item['text']}"
        btn = ttk.Button(button_frame,
                        text=button_text,
                        command=lambda item_id=menu_item['id']: self._on_menu_click(item_id),
                        style=style)
        btn.pack(fill='x')
        
        # Salva il riferimento per aggiornamenti futuri
        self.menu_buttons[menu_item['id']] = btn
        
        # Effetti hover
        self._add_menu_hover_effects(btn, menu_item['id'])
    
    def _add_menu_hover_effects(self, button: ttk.Button, menu_id: str):
        """Aggiunge effetti hover ai pulsanti del menu"""
        def on_enter(event):
            if menu_id != self.active_menu:
                button.configure(cursor='hand2')
        
        def on_leave(event):
            button.configure(cursor='')
        
        button.bind('<Enter>', on_enter)
        button.bind('<Leave>', on_leave)
    
    def _create_content_area(self, parent: tk.Widget):
        """Crea l'area del contenuto principale"""
        self.content_area = ttk.Frame(parent, style='Section.TFrame')
        self.content_area.pack(side='right', fill='both', expand=True)
    
    def _on_menu_click(self, menu_id: str):
        """Gestisce il click su un elemento del menu"""
        if menu_id == self.active_menu:
            return
        
        # Aggiorna il menu attivo
        self._update_active_menu(menu_id)
        
        # Carica il contenuto corrispondente
        self._load_content(menu_id)
    
    def _update_active_menu(self, new_active: str):
        """Aggiorna lo stile del menu attivo"""
        # Reset del pulsante precedentemente attivo
        if self.active_menu in self.menu_buttons:
            self.menu_buttons[self.active_menu].configure(style='Secondary.TButton')
        
        # Imposta il nuovo pulsante attivo
        if new_active in self.menu_buttons:
            self.menu_buttons[new_active].configure(style='Primary.TButton')
        
        self.active_menu = new_active
    
    def _load_content(self, content_id: str):
        """Carica il contenuto per la sezione specificata"""
        # Pulisce il contenuto esistente
        if self.current_content:
            self.current_content.destroy()
        
        # Crea il nuovo contenuto
        if content_id == 'home':
            self.current_content = self._create_home_content()
        elif content_id == 'team':
            self.current_content = TeamMenu(self.content_frame, self.club_data, self.game_engine)
        elif content_id == 'tactics':
            self.current_content = TacticsMenu(self.content_frame, self.club_data, self.game_engine)
        elif content_id == 'transfers':
            self.current_content = TransfersMenu(self.content_frame, self.club_data, self.game_engine)
        elif content_id == 'finances':
            self.current_content = FinancesMenu(self.content_frame, self.club_data, self.game_engine)
        elif content_id == 'facilities':
            self.current_content = FacilitiesMenu(self.content_frame, self.club_data, self.game_engine)
        elif content_id == 'staff':
            self.current_content = StaffMenu(self.content_frame, self.club_data, self.game_engine)
        elif content_id == 'youth':
            self.current_content = YouthMenu(self.content_frame, self.club_data, self.game_engine)
        elif content_id == 'calendar':
            self.current_content = CalendarMenu(self.content_frame, self.club_data, self.game_engine)
        elif content_id == 'statistics':
            self.current_content = StatisticsMenu(self.content_frame, self.club_data, self.game_engine)
        elif content_id == 'media':
            self.current_content = MediaMenu(self.content_frame, self.club_data, self.game_engine)
        elif content_id == 'board':
            self.current_content = BoardMenu(self.content_frame, self.club_data, self.game_engine)
        else:
            self.current_content = self._create_placeholder_content("Sezione in Sviluppo",
                                                                   "Questa sezione è ancora in fase di sviluppo.")
    
    def _create_home_content(self) -> ttk.Frame:
        """Crea il contenuto della home page"""
        home_frame = ttk.Frame(self.content_area)
        home_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Benvenuto
        welcome_label = ttk.Label(home_frame,
                                text=f"Benvenuto, Presidente del {self.selected_club['nome']}!",
                                font=FONTS['title'],
                                foreground=COLORS['primary'])
        welcome_label.pack(pady=(0, 20))
        
        # Informazioni rapide del club
        info_frame = ttk.LabelFrame(home_frame, text="Informazioni Club", padding=15)
        info_frame.pack(fill='x', pady=(0, 20))
        
        # Grid per le informazioni
        info_grid = ttk.Frame(info_frame)
        info_grid.pack(fill='x')
        
        # Colonna sinistra
        left_col = ttk.Frame(info_grid)
        left_col.pack(side='left', fill='both', expand=True)
        
        left_info = [
            ("Presidente", self.selected_club.get('presidente', 'N/A')),
            ("Città", self.selected_club['citta']),
            ("Stadio", self.selected_club['stadio']),
            ("Capienza", f"{self.selected_club['capienza_stadio']:,}")
        ]
        
        for label, value in left_info:
            self._create_info_row(left_col, label, value)
        
        # Colonna destra
        right_col = ttk.Frame(info_grid)
        right_col.pack(side='right', fill='both', expand=True)
        
        right_info = [
            ("Reputazione", f"{self.selected_club['reputazione']}/20"),
            ("Budget Trasferimenti", f"€{self.selected_club['budget_trasferimenti_eur']:,}"),
            ("Budget Stipendi", f"€{self.selected_club['budget_stipendi_eur']:,}"),
            ("Formazione", self.selected_club['formazione_predefinita'])
        ]
        
        for label, value in right_info:
            self._create_info_row(right_col, label, value)
        
        # Azioni rapide
        actions_frame = ttk.LabelFrame(home_frame, text="Azioni Rapide", padding=15)
        actions_frame.pack(fill='x')
        
        quick_actions = [
            ("Vedi Squadra", lambda: self._on_menu_click('team')),
            ("Imposta Tattica", lambda: self._on_menu_click('tactics')),
            ("Mercato", lambda: self._on_menu_click('transfers')),
            ("Calendario", lambda: self._on_menu_click('calendar'))
        ]
        
        actions_grid = ttk.Frame(actions_frame)
        actions_grid.pack()
        
        for i, (text, command) in enumerate(quick_actions):
            btn = ttk.Button(actions_grid, text=text, command=command, style='Primary.TButton')
            btn.grid(row=i//2, column=i%2, padx=10, pady=5, sticky='ew')
        
        return home_frame
    
    def _create_placeholder_content(self, title: str, description: str) -> ttk.Frame:
        """Crea contenuto placeholder per sezioni non ancora implementate"""
        placeholder_frame = ttk.Frame(self.content_area)
        placeholder_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Titolo
        title_label = ttk.Label(placeholder_frame,
                              text=title,
                              font=FONTS['title'],
                              foreground=COLORS['primary'])
        title_label.pack(pady=(0, 10))
        
        # Descrizione
        desc_label = ttk.Label(placeholder_frame,
                             text=description,
                             font=FONTS['normal'],
                             foreground=COLORS['text_secondary'])
        desc_label.pack(pady=(0, 20))
        
        # Messaggio di sviluppo
        dev_label = ttk.Label(placeholder_frame,
                            text="🚧 Sezione in sviluppo 🚧",
                            font=FONTS['subtitle'],
                            foreground=COLORS['warning'])
        dev_label.pack()
        
        return placeholder_frame
    
    def _create_info_row(self, parent: tk.Widget, label: str, value: str):
        """Crea una riga di informazioni"""
        row_frame = ttk.Frame(parent)
        row_frame.pack(fill='x', pady=2)
        
        label_widget = ttk.Label(row_frame, text=f"{label}:", 
                               font=FONTS['normal'], foreground=COLORS['text_secondary'])
        label_widget.pack(side='left')
        
        value_widget = ttk.Label(row_frame, text=value, 
                               font=FONTS['normal'], foreground=COLORS['text_primary'])
        value_widget.pack(side='right')
    
    def _on_save_game(self):
        """Gestisce il salvataggio del gioco"""
        self.trigger_callback('save_game')
    
    def _on_settings(self):
        """Gestisce l'apertura delle impostazioni"""
        self.show_message("Impostazioni", "Funzionalità in sviluppo", "info")
    
    def _on_back_to_menu(self):
        """Gestisce il ritorno al menu principale"""
        confirm = self.show_message(
            "Sei sicuro di voler tornare al menu principale?",
            "Conferma",
            "question"
        )
        
        if confirm:
            self.trigger_callback('back_to_menu')
    
    def setup_events(self):
        """Configura gli eventi della dashboard"""
        if not self.window:
            return
        
        # Tasti scorciatoia
        self.window.bind('<Control-s>', lambda e: self._on_save_game())
        self.window.bind('<Escape>', lambda e: self._on_back_to_menu())
        
        # Focus sulla finestra
        self.window.focus_set()
    
    def create_window(self, width: int = 1400, height: int = 900, resizable: bool = True):
        """Crea la finestra della dashboard"""
        return super().create_window(width, height, resizable)
