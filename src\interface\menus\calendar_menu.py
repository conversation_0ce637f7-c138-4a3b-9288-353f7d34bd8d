"""
Menu Calendario
Visualizza partite, allenamenti e eventi del club
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
from typing import Dict, List
from .base_menu import BaseMenu
from ..gui_config import COLORS, FONTS, ICONS


class CalendarMenu(BaseMenu):
    """Menu per la gestione del calendario"""
    
    def __init__(self, parent: tk.Widget, club_data: Dict, game_engine, data_loader):
        super().__init__(parent, club_data, game_engine, data_loader)
        
        # Data corrente (dal game engine o data attuale)
        self.current_date = game_engine.state.current_date if game_engine else datetime.now()
        
        # Genera eventi di esempio
        self.events = self._generate_sample_events()
    
    def setup_ui(self):
        """Configura l'interfaccia del menu calendario"""
        # Header
        self.create_section_header(self.main_frame, "Calendario", 
                                 f"Programma del {self.club_data['nome']}")
        
        # Crea le schede per diverse visualizzazioni
        tabs_config = [
            {'text': 'Panoramica', 'setup_func': self._setup_overview_tab},
            {'text': 'Partite', 'setup_func': self._setup_matches_tab},
            {'text': 'Allenamenti', 'setup_func': self._setup_training_tab},
            {'text': 'Eventi', 'setup_func': self._setup_events_tab}
        ]
        
        self.notebook = self.create_tabs(self.main_frame, tabs_config)
    
    def _setup_overview_tab(self, parent: tk.Widget):
        """Configura la scheda panoramica"""
        # Informazioni data corrente
        date_frame = ttk.LabelFrame(parent, text="Data Corrente", padding=15)
        date_frame.pack(fill='x', pady=(0, 10))
        
        date_info = [
            ("Data", self.current_date.strftime("%d/%m/%Y")),
            ("Giorno", self._get_day_name(self.current_date.weekday())),
            ("Settimana", f"Settimana {self.current_date.isocalendar()[1]}"),
            ("Stagione", "2025/2026")
        ]
        
        date_grid = ttk.Frame(date_frame)
        date_grid.pack(fill='x')
        
        for i, (label, value) in enumerate(date_info):
            col = i % 2
            row = i // 2
            
            info_frame = ttk.Frame(date_grid)
            info_frame.grid(row=row, column=col, sticky='ew', padx=10, pady=2)
            date_grid.columnconfigure(col, weight=1)
            
            self.create_info_row(info_frame, label, value)
        
        # Prossimi eventi
        upcoming_frame = ttk.LabelFrame(parent, text="Prossimi Eventi (7 giorni)", padding=15)
        upcoming_frame.pack(fill='x', pady=10)
        
        upcoming_events = self._get_upcoming_events(7)
        
        if upcoming_events:
            for event in upcoming_events[:5]:  # Mostra max 5 eventi
                event_frame = ttk.Frame(upcoming_frame)
                event_frame.pack(fill='x', pady=3)
                
                # Icona evento
                icon = self._get_event_icon(event['type'])
                ttk.Label(event_frame, text=icon, font=FONTS['normal']).pack(side='left', padx=(0, 10))
                
                # Data
                date_str = event['date'].strftime("%d/%m")
                ttk.Label(event_frame, text=date_str, font=FONTS['small'], 
                         foreground=COLORS['text_secondary']).pack(side='left', padx=(0, 10))
                
                # Descrizione
                ttk.Label(event_frame, text=event['description'], 
                         font=FONTS['normal']).pack(side='left')
                
                # Ora
                if 'time' in event:
                    ttk.Label(event_frame, text=event['time'], font=FONTS['small'],
                             foreground=COLORS['text_secondary']).pack(side='right')
        else:
            ttk.Label(upcoming_frame, text="Nessun evento programmato nei prossimi 7 giorni",
                     font=FONTS['normal'], foreground=COLORS['text_secondary']).pack()
        
        # Statistiche mensili
        stats_frame = ttk.LabelFrame(parent, text="Statistiche Mese Corrente", padding=15)
        stats_frame.pack(fill='both', expand=True)
        
        monthly_stats = self._calculate_monthly_stats()
        
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill='x')
        
        left_stats = ttk.Frame(stats_grid)
        left_stats.pack(side='left', fill='both', expand=True)
        
        right_stats = ttk.Frame(stats_grid)
        right_stats.pack(side='right', fill='both', expand=True)
        
        left_data = [
            ("Partite Giocate", str(monthly_stats['matches_played'])),
            ("Allenamenti", str(monthly_stats['training_sessions'])),
            ("Giorni di Riposo", str(monthly_stats['rest_days']))
        ]
        
        right_data = [
            ("Vittorie", str(monthly_stats['wins'])),
            ("Eventi Speciali", str(monthly_stats['special_events'])),
            ("Intensità Media", f"{monthly_stats['avg_intensity']}%")
        ]
        
        for label, value in left_data:
            self.create_info_row(left_stats, label, value)
        
        for label, value in right_data:
            self.create_info_row(right_stats, label, value)
    
    def _setup_matches_tab(self, parent: tk.Widget):
        """Configura la scheda partite"""
        # Controlli filtro
        filter_frame = ttk.Frame(parent)
        filter_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(filter_frame, text="Competizione:").pack(side='left', padx=(0, 5))
        competition_combo = ttk.Combobox(filter_frame, 
                                       values=['Tutte', 'Serie A', 'Coppa Italia', 'Champions League'],
                                       state='readonly', width=15)
        competition_combo.set('Tutte')
        competition_combo.pack(side='left', padx=(0, 20))
        
        ttk.Label(filter_frame, text="Periodo:").pack(side='left', padx=(0, 5))
        period_combo = ttk.Combobox(filter_frame,
                                  values=['Prossime 2 settimane', 'Mese corrente', 'Stagione completa'],
                                  state='readonly', width=20)
        period_combo.set('Prossime 2 settimane')
        period_combo.pack(side='left')
        
        # Tabella partite
        matches_frame = ttk.LabelFrame(parent, text="Calendario Partite", padding=10)
        matches_frame.pack(fill='both', expand=True)
        
        matches_columns = [
            {'id': 'date', 'text': 'Data', 'width': 80},
            {'id': 'time', 'text': 'Ora', 'width': 60},
            {'id': 'competition', 'text': 'Competizione', 'width': 120},
            {'id': 'opponent', 'text': 'Avversario', 'width': 150},
            {'id': 'venue', 'text': 'Sede', 'width': 80},
            {'id': 'status', 'text': 'Stato', 'width': 100}
        ]
        
        matches_data = self._get_matches_data()
        
        matches_tree = self.create_data_table(matches_frame, matches_columns, matches_data, height=12)
        
        # Pulsanti controllo
        matches_buttons = [
            {'text': '📋 Dettagli Partita', 'command': self._show_match_details, 'style': 'Primary.TButton'},
            {'text': '⚙️ Imposta Tattica', 'command': self._set_tactics, 'style': 'Secondary.TButton'},
            {'text': '📊 Statistiche', 'command': self._show_match_stats, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(matches_frame, matches_buttons)
    
    def _setup_training_tab(self, parent: tk.Widget):
        """Configura la scheda allenamenti"""
        # Programma allenamenti
        training_frame = ttk.LabelFrame(parent, text="Programma Allenamenti", padding=15)
        training_frame.pack(fill='x', pady=(0, 10))
        
        training_columns = [
            {'id': 'date', 'text': 'Data', 'width': 100},
            {'id': 'type', 'text': 'Tipo', 'width': 120},
            {'id': 'focus', 'text': 'Focus', 'width': 150},
            {'id': 'intensity', 'text': 'Intensità', 'width': 80},
            {'id': 'duration', 'text': 'Durata', 'width': 80},
            {'id': 'status', 'text': 'Stato', 'width': 100}
        ]
        
        training_data = [
            ["15/01/2025", "Tecnico", "Passaggi e controllo", "Media", "90 min", "Programmato"],
            ["16/01/2025", "Fisico", "Resistenza", "Alta", "75 min", "Programmato"],
            ["17/01/2025", "Tattico", "Schemi offensivi", "Media", "85 min", "Programmato"],
            ["18/01/2025", "Riposo", "-", "-", "-", "Programmato"],
            ["19/01/2025", "Tecnico", "Finalizzazione", "Media", "80 min", "Programmato"],
            ["20/01/2025", "Tattico", "Preparazione partita", "Bassa", "60 min", "Programmato"]
        ]
        
        self.create_data_table(training_frame, training_columns, training_data, height=8)
        
        # Controlli allenamento
        training_controls = ttk.LabelFrame(parent, text="Gestione Allenamenti", padding=15)
        training_controls.pack(fill='x', pady=10)
        
        # Intensità settimanale
        intensity_frame = ttk.Frame(training_controls)
        intensity_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(intensity_frame, text="Intensità Settimanale:", font=FONTS['normal']).pack(side='left')
        
        intensity_var = tk.StringVar(value="Media")
        intensity_combo = ttk.Combobox(intensity_frame, textvariable=intensity_var,
                                     values=['Bassa', 'Media', 'Alta', 'Molto Alta'],
                                     state='readonly', width=12)
        intensity_combo.pack(side='left', padx=(10, 0))
        
        # Focus principale
        focus_frame = ttk.Frame(training_controls)
        focus_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(focus_frame, text="Focus Principale:", font=FONTS['normal']).pack(side='left')
        
        focus_var = tk.StringVar(value="Equilibrato")
        focus_combo = ttk.Combobox(focus_frame, textvariable=focus_var,
                                 values=['Tecnico', 'Fisico', 'Tattico', 'Equilibrato'],
                                 state='readonly', width=12)
        focus_combo.pack(side='left', padx=(10, 0))
        
        # Pulsanti controllo
        training_buttons = [
            {'text': '📅 Modifica Programma', 'command': self._modify_training, 'style': 'Primary.TButton'},
            {'text': '📊 Report Allenamenti', 'command': self._training_report, 'style': 'Secondary.TButton'},
            {'text': '⚙️ Impostazioni', 'command': self._training_settings, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(training_controls, training_buttons)
        
        # Statistiche allenamento
        stats_frame = ttk.LabelFrame(parent, text="Statistiche Allenamento", padding=15)
        stats_frame.pack(fill='both', expand=True)
        
        training_stats = [
            ("Sessioni Completate", "18/20"),
            ("Intensità Media", "72%"),
            ("Partecipazione Media", "94%"),
            ("Infortuni in Allenamento", "2"),
            ("Miglioramento Fitness", "+8%"),
            ("Soddisfazione Squadra", "85%")
        ]
        
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill='x')
        
        for i, (label, value) in enumerate(training_stats):
            col = i % 3
            row = i // 3
            
            stat_frame = ttk.Frame(stats_grid)
            stat_frame.grid(row=row, column=col, sticky='ew', padx=10, pady=5)
            stats_grid.columnconfigure(col, weight=1)
            
            self.create_info_row(stat_frame, label, value)
    
    def _setup_events_tab(self, parent: tk.Widget):
        """Configura la scheda eventi"""
        # Eventi speciali
        events_frame = ttk.LabelFrame(parent, text="Eventi e Appuntamenti", padding=15)
        events_frame.pack(fill='both', expand=True)
        
        events_columns = [
            {'id': 'date', 'text': 'Data', 'width': 100},
            {'id': 'type', 'text': 'Tipo', 'width': 120},
            {'id': 'description', 'text': 'Descrizione', 'width': 250},
            {'id': 'priority', 'text': 'Priorità', 'width': 80},
            {'id': 'status', 'text': 'Stato', 'width': 100}
        ]
        
        events_data = [
            ["18/01/2025", "Riunione", "Consiglio di Amministrazione", "Alta", "Programmato"],
            ["20/01/2025", "Media", "Conferenza stampa pre-partita", "Media", "Programmato"],
            ["22/01/2025", "Mercato", "Incontro con agente", "Alta", "Programmato"],
            ["25/01/2025", "Evento", "Presentazione nuovo sponsor", "Media", "Programmato"],
            ["28/01/2025", "Medico", "Visite mediche giocatori", "Bassa", "Programmato"],
            ["30/01/2025", "Scadenza", "Chiusura mercato invernale", "Critica", "Programmato"]
        ]
        
        self.create_data_table(events_frame, events_columns, events_data, height=10)
        
        # Controlli eventi
        events_buttons = [
            {'text': '➕ Nuovo Evento', 'command': self._add_event, 'style': 'Primary.TButton'},
            {'text': '✏️ Modifica', 'command': self._edit_event, 'style': 'Secondary.TButton'},
            {'text': '🗑️ Elimina', 'command': self._delete_event, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(events_frame, events_buttons)
    
    def _generate_sample_events(self) -> List[Dict]:
        """Genera eventi di esempio"""
        events = []
        base_date = self.current_date
        
        # Partite
        for i in range(10):
            event_date = base_date + timedelta(days=i*7)
            events.append({
                'date': event_date,
                'type': 'match',
                'description': f'vs {["Juventus", "Milan", "Inter", "Roma", "Lazio"][i % 5]}',
                'time': '20:45'
            })
        
        # Allenamenti
        for i in range(30):
            if i % 7 != 6:  # Non domenica
                event_date = base_date + timedelta(days=i)
                events.append({
                    'date': event_date,
                    'type': 'training',
                    'description': 'Allenamento',
                    'time': '10:00'
                })
        
        return events
    
    def _get_upcoming_events(self, days: int) -> List[Dict]:
        """Ottiene gli eventi dei prossimi giorni"""
        end_date = self.current_date + timedelta(days=days)
        upcoming = [e for e in self.events if self.current_date <= e['date'] <= end_date]
        return sorted(upcoming, key=lambda x: x['date'])
    
    def _get_event_icon(self, event_type: str) -> str:
        """Restituisce l'icona per il tipo di evento"""
        icons = {
            'match': '⚽',
            'training': '🏃',
            'meeting': '🏛️',
            'media': '📺',
            'medical': '🏥',
            'event': '📅'
        }
        return icons.get(event_type, '📅')
    
    def _get_day_name(self, weekday: int) -> str:
        """Converte il numero del giorno in nome"""
        days = ['Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato', 'Domenica']
        return days[weekday]
    
    def _calculate_monthly_stats(self) -> Dict:
        """Calcola le statistiche mensili"""
        return {
            'matches_played': 4,
            'training_sessions': 18,
            'rest_days': 6,
            'wins': 3,
            'special_events': 2,
            'avg_intensity': 75
        }
    
    def _get_matches_data(self) -> List[List]:
        """Ottiene i dati delle partite per la tabella"""
        return [
            ["18/01", "20:45", "Serie A", "vs Juventus", "Casa", "Programmata"],
            ["25/01", "15:00", "Serie A", "@ Milan", "Trasferta", "Programmata"],
            ["01/02", "18:30", "Coppa Italia", "vs Roma", "Casa", "Programmata"],
            ["08/02", "20:45", "Serie A", "@ Inter", "Trasferta", "Programmata"],
            ["15/02", "21:00", "Champions League", "vs Bayern", "Casa", "Programmata"],
            ["22/02", "20:45", "Serie A", "vs Lazio", "Casa", "Programmata"]
        ]
    
    # Metodi placeholder per le azioni
    def _show_match_details(self):
        self.show_message("Dettagli Partita", "Funzionalità in sviluppo", "info")
    
    def _set_tactics(self):
        self.show_message("Imposta Tattica", "Funzionalità in sviluppo", "info")
    
    def _show_match_stats(self):
        self.show_message("Statistiche Partita", "Funzionalità in sviluppo", "info")
    
    def _modify_training(self):
        self.show_message("Modifica Programma", "Funzionalità in sviluppo", "info")
    
    def _training_report(self):
        self.show_message("Report Allenamenti", "Funzionalità in sviluppo", "info")
    
    def _training_settings(self):
        self.show_message("Impostazioni Allenamento", "Funzionalità in sviluppo", "info")
    
    def _add_event(self):
        self.show_message("Nuovo Evento", "Funzionalità in sviluppo", "info")
    
    def _edit_event(self):
        self.show_message("Modifica Evento", "Funzionalità in sviluppo", "info")
    
    def _delete_event(self):
        self.show_message("Elimina Evento", "Funzionalità in sviluppo", "info")
