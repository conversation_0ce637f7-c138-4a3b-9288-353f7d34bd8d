"""
Classe base per tutti i menu dell'interfaccia
Fornisce funzionalità comuni per i menu modulari
"""

import tkinter as tk
from tkinter import ttk
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from ..gui_config import COLORS, FONTS, COMPONENT_SIZES, TEXTS, ICONS


class BaseMenu(ABC):
    """Classe base astratta per tutti i menu"""
    
    def __init__(self, parent: tk.Widget, club_data: Dict, game_engine: Any, data_loader: Any):
        self.parent = parent
        self.club_data = club_data
        self.game_engine = game_engine
        self.data_loader = data_loader
        
        # Widget principale del menu
        self.main_frame = None
        
        # Callbacks per comunicazione con la dashboard
        self.callbacks = {}
    
    def create_menu(self) -> ttk.Frame:
        """Crea e restituisce il frame principale del menu"""
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Configura l'interfaccia specifica del menu
        self.setup_ui()
        
        return self.main_frame
    
    @abstractmethod
    def setup_ui(self):
        """Metodo astratto per configurare l'interfaccia del menu"""
        pass
    
    def create_section_header(self, parent: tk.Widget, title: str, subtitle: str = "") -> ttk.Frame:
        """Crea un header per una sezione del menu"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill='x', pady=(0, 15))
        
        # Titolo principale
        title_label = ttk.Label(header_frame, text=title, font=FONTS['title'], 
                              foreground=COLORS['primary'])
        title_label.pack(anchor='w')
        
        # Sottotitolo opzionale
        if subtitle:
            subtitle_label = ttk.Label(header_frame, text=subtitle, font=FONTS['normal'],
                                     foreground=COLORS['text_secondary'])
            subtitle_label.pack(anchor='w', pady=(2, 0))
        
        # Separatore
        ttk.Separator(header_frame, orient='horizontal').pack(fill='x', pady=(10, 0))
        
        return header_frame
    
    def create_info_card(self, parent: tk.Widget, title: str, items: List[tuple]) -> ttk.Frame:
        """Crea una card informativa con lista di elementi"""
        card_frame = ttk.LabelFrame(parent, text=title, padding=15)
        card_frame.pack(fill='x', pady=10)
        
        for label, value in items:
            self.create_info_row(card_frame, label, value)
        
        return card_frame
    
    def create_info_row(self, parent: tk.Widget, label: str, value: str, 
                       value_color: str = None) -> ttk.Frame:
        """Crea una riga di informazioni"""
        row_frame = ttk.Frame(parent)
        row_frame.pack(fill='x', pady=2)
        
        # Etichetta
        label_widget = ttk.Label(row_frame, text=f"{label}:", 
                               font=FONTS['normal'], foreground=COLORS['text_secondary'])
        label_widget.pack(side='left')
        
        # Valore
        color = value_color or COLORS['text_primary']
        value_widget = ttk.Label(row_frame, text=str(value), 
                               font=FONTS['normal'], foreground=color)
        value_widget.pack(side='right')
        
        return row_frame
    
    def create_button_group(self, parent: tk.Widget, buttons: List[Dict]) -> ttk.Frame:
        """Crea un gruppo di pulsanti"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill='x', pady=10)
        
        for button_config in buttons:
            text = button_config.get('text', '')
            command = button_config.get('command', None)
            style = button_config.get('style', 'Primary.TButton')
            side = button_config.get('side', 'left')
            
            btn = ttk.Button(button_frame, text=text, command=command, style=style)
            btn.pack(side=side, padx=5)
        
        return button_frame
    
    def create_data_table(self, parent: tk.Widget, columns: List[Dict], 
                         data: List[List], height: int = 10) -> ttk.Treeview:
        """Crea una tabella per visualizzare dati"""
        # Frame per la tabella
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill='both', expand=True, pady=10)
        
        # Configura le colonne
        column_ids = [col['id'] for col in columns]
        tree = ttk.Treeview(table_frame, columns=column_ids, show='headings', height=height)
        
        # Configura headers e larghezze
        for col in columns:
            tree.heading(col['id'], text=col['text'])
            tree.column(col['id'], width=col.get('width', 100))
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack
        tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Popola i dati
        for row in data:
            tree.insert('', 'end', values=row)
        
        return tree
    
    def create_progress_bar(self, parent: tk.Widget, label: str, 
                          value: int, max_value: int = 100) -> ttk.Frame:
        """Crea una barra di progresso con etichetta"""
        progress_frame = ttk.Frame(parent)
        progress_frame.pack(fill='x', pady=5)
        
        # Etichetta
        label_frame = ttk.Frame(progress_frame)
        label_frame.pack(fill='x')
        
        ttk.Label(label_frame, text=label, font=FONTS['normal']).pack(side='left')
        ttk.Label(label_frame, text=f"{value}/{max_value}", 
                 font=FONTS['small'], foreground=COLORS['text_secondary']).pack(side='right')
        
        # Barra di progresso
        progress = ttk.Progressbar(progress_frame, length=200, mode='determinate')
        progress.pack(fill='x', pady=(2, 0))
        progress['maximum'] = max_value
        progress['value'] = value
        
        return progress_frame
    
    def create_tabs(self, parent: tk.Widget, tabs: List[Dict]) -> ttk.Notebook:
        """Crea un widget a schede"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True, pady=10)
        
        for tab_config in tabs:
            tab_frame = ttk.Frame(notebook)
            notebook.add(tab_frame, text=tab_config['text'])
            
            # Chiama la funzione di setup se fornita
            if 'setup_func' in tab_config:
                tab_config['setup_func'](tab_frame)
        
        return notebook
    
    def format_currency(self, amount: int) -> str:
        """Formatta un importo in valuta"""
        if amount >= 1_000_000:
            return f"€{amount / 1_000_000:.1f}M"
        elif amount >= 1_000:
            return f"€{amount / 1_000:.0f}K"
        else:
            return f"€{amount:,}"
    
    def format_percentage(self, value: float, decimals: int = 1) -> str:
        """Formatta una percentuale"""
        return f"{value:.{decimals}f}%"
    
    def get_reputation_text(self, reputation: int) -> str:
        """Converte il valore numerico di reputazione in testo"""
        if reputation >= 18:
            return "Mondiale"
        elif reputation >= 15:
            return "Continentale"
        elif reputation >= 12:
            return "Nazionale"
        elif reputation >= 8:
            return "Regionale"
        else:
            return "Locale"
    
    def get_condition_text(self, condition: int) -> str:
        """Converte il valore numerico di condizione in testo"""
        if condition >= 9:
            return "Eccellente"
        elif condition >= 7:
            return "Buona"
        elif condition >= 5:
            return "Discreta"
        elif condition >= 3:
            return "Scarsa"
        else:
            return "Pessima"
    
    def add_callback(self, event: str, callback):
        """Aggiunge un callback per un evento"""
        self.callbacks[event] = callback
    
    def trigger_callback(self, event: str, *args, **kwargs):
        """Esegue un callback se presente"""
        if event in self.callbacks:
            return self.callbacks[event](*args, **kwargs)
    
    def show_message(self, title: str, message: str, msg_type: str = "info"):
        """Mostra un messaggio all'utente"""
        from tkinter import messagebox
        
        if msg_type == "error":
            messagebox.showerror(title, message)
        elif msg_type == "warning":
            messagebox.showwarning(title, message)
        elif msg_type == "question":
            return messagebox.askyesno(title, message)
        else:
            messagebox.showinfo(title, message)
    
    def refresh_data(self):
        """Aggiorna i dati del menu (da implementare nelle sottoclassi se necessario)"""
        pass
    
    def cleanup(self):
        """Pulisce le risorse del menu (da implementare nelle sottoclassi se necessario)"""
        pass
