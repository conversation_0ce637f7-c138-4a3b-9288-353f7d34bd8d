"""
Menu Gestione Squadra
Visualizza e gestisce i giocatori della squadra
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List
from .base_menu import BaseMenu
from ..gui_config import COLORS, FONTS, ICONS


class TeamMenu(BaseMenu):
    """Menu per la gestione della squadra"""
    
    def __init__(self, parent: tk.Widget, club_data: Dict, game_engine, data_loader):
        super().__init__(parent, club_data, game_engine, data_loader)
        
        # Dati dei giocatori (placeholder - da sostituire con dati reali)
        self.players_data = self._generate_sample_players()
        self.selected_player = None
    
    def setup_ui(self):
        """Configura l'interfaccia del menu squadra"""
        # Header
        self.create_section_header(self.main_frame, "Gestione Squadra", 
                                 f"Rosa del {self.club_data['nome']}")
        
        # Crea le schede per diverse visualizzazioni
        tabs_config = [
            {'text': 'Rosa Completa', 'setup_func': self._setup_full_squad_tab},
            {'text': 'Formazione', 'setup_func': self._setup_formation_tab},
            {'text': 'Statistiche', 'setup_func': self._setup_stats_tab}
        ]
        
        self.notebook = self.create_tabs(self.main_frame, tabs_config)
    
    def _setup_full_squad_tab(self, parent: tk.Widget):
        """Configura la scheda della rosa completa"""
        # Frame per filtri
        filters_frame = ttk.Frame(parent)
        filters_frame.pack(fill='x', pady=(0, 10))
        
        # Filtri
        ttk.Label(filters_frame, text="Posizione:").pack(side='left', padx=(0, 5))
        position_combo = ttk.Combobox(filters_frame, values=['Tutte', 'Portieri', 'Difensori', 'Centrocampisti', 'Attaccanti'], 
                                    state='readonly', width=15)
        position_combo.set('Tutte')
        position_combo.pack(side='left', padx=(0, 20))
        
        ttk.Label(filters_frame, text="Età:").pack(side='left', padx=(0, 5))
        age_combo = ttk.Combobox(filters_frame, values=['Tutte', 'Under 21', '21-25', '26-30', 'Over 30'], 
                               state='readonly', width=10)
        age_combo.set('Tutte')
        age_combo.pack(side='left')
        
        # Tabella giocatori
        columns = [
            {'id': 'name', 'text': 'Nome', 'width': 150},
            {'id': 'position', 'text': 'Ruolo', 'width': 80},
            {'id': 'age', 'text': 'Età', 'width': 50},
            {'id': 'nationality', 'text': 'Nazionalità', 'width': 100},
            {'id': 'rating', 'text': 'Valutazione', 'width': 80},
            {'id': 'value', 'text': 'Valore', 'width': 100},
            {'id': 'contract', 'text': 'Contratto', 'width': 80}
        ]
        
        self.players_tree = self.create_data_table(parent, columns, self._get_players_table_data())
        self.players_tree.bind('<<TreeviewSelect>>', self._on_player_selected)
        
        # Frame per dettagli giocatore
        self._create_player_details_frame(parent)
    
    def _setup_formation_tab(self, parent: tk.Widget):
        """Configura la scheda della formazione"""
        # Informazioni formazione attuale
        formation_info = ttk.LabelFrame(parent, text="Formazione Attuale", padding=15)
        formation_info.pack(fill='x', pady=(0, 10))
        
        current_formation = self.club_data.get('formazione_predefinita', '4-3-3')
        ttk.Label(formation_info, text=f"Modulo: {current_formation}", 
                 font=FONTS['subtitle']).pack(anchor='w')
        
        # Placeholder per visualizzazione campo
        field_frame = ttk.LabelFrame(parent, text="Campo di Gioco", padding=20)
        field_frame.pack(fill='both', expand=True)
        
        # Canvas per il campo (placeholder)
        canvas = tk.Canvas(field_frame, bg='green', height=400)
        canvas.pack(fill='both', expand=True)
        
        # Disegna un campo semplificato
        canvas.create_rectangle(50, 50, 350, 350, outline='white', width=2)
        canvas.create_line(200, 50, 200, 350, fill='white', width=2)
        canvas.create_oval(175, 175, 225, 225, outline='white', width=2)
        
        # Placeholder per posizioni giocatori
        canvas.create_text(200, 100, text="Formazione\n(In sviluppo)", fill='white', font=FONTS['subtitle'])
    
    def _setup_stats_tab(self, parent: tk.Widget):
        """Configura la scheda delle statistiche"""
        # Statistiche squadra
        team_stats_frame = ttk.LabelFrame(parent, text="Statistiche Squadra", padding=15)
        team_stats_frame.pack(fill='x', pady=(0, 10))
        
        # Statistiche generali
        stats_data = [
            ("Giocatori Totali", len(self.players_data)),
            ("Età Media", "25.3 anni"),
            ("Valore Rosa", "€45.2M"),
            ("Stipendi Totali", self.format_currency(self.club_data.get('budget_stipendi_eur', 0))),
            ("Giocatori Stranieri", "12"),
            ("Giocatori Under 21", "5")
        ]
        
        # Crea due colonne per le statistiche
        stats_grid = ttk.Frame(team_stats_frame)
        stats_grid.pack(fill='x')
        
        left_stats = ttk.Frame(stats_grid)
        left_stats.pack(side='left', fill='both', expand=True)
        
        right_stats = ttk.Frame(stats_grid)
        right_stats.pack(side='right', fill='both', expand=True)
        
        for i, (label, value) in enumerate(stats_data):
            parent_frame = left_stats if i < len(stats_data) // 2 else right_stats
            self.create_info_row(parent_frame, label, value)
        
        # Top giocatori
        top_players_frame = ttk.LabelFrame(parent, text="Top Giocatori", padding=15)
        top_players_frame.pack(fill='both', expand=True)
        
        # Tabella top giocatori
        top_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 150},
            {'id': 'position', 'text': 'Ruolo', 'width': 80},
            {'id': 'rating', 'text': 'Valutazione', 'width': 80},
            {'id': 'goals', 'text': 'Gol', 'width': 50},
            {'id': 'assists', 'text': 'Assist', 'width': 50},
            {'id': 'appearances', 'text': 'Presenze', 'width': 70}
        ]
        
        top_players_data = [
            ["Marco Rossi", "ATT", "85", "12", "5", "18"],
            ["Luca Bianchi", "CC", "82", "3", "8", "20"],
            ["Giuseppe Verdi", "DIF", "80", "1", "2", "19"],
            ["Antonio Neri", "POR", "78", "0", "0", "15"],
            ["Francesco Blu", "ATT", "77", "8", "3", "16"]
        ]
        
        self.create_data_table(top_players_frame, top_columns, top_players_data, height=6)
    
    def _create_player_details_frame(self, parent: tk.Widget):
        """Crea il frame per i dettagli del giocatore selezionato"""
        details_frame = ttk.LabelFrame(parent, text="Dettagli Giocatore", padding=15)
        details_frame.pack(fill='x', pady=(10, 0))
        
        self.player_details_label = ttk.Label(details_frame, 
                                            text="Seleziona un giocatore per vedere i dettagli",
                                            font=FONTS['normal'],
                                            foreground=COLORS['text_secondary'])
        self.player_details_label.pack()
    
    def _on_player_selected(self, event):
        """Gestisce la selezione di un giocatore"""
        selection = self.players_tree.selection()
        if not selection:
            return
        
        item = self.players_tree.item(selection[0])
        player_name = item['values'][0]
        
        # Trova il giocatore nei dati
        self.selected_player = None
        for player in self.players_data:
            if player['name'] == player_name:
                self.selected_player = player
                break
        
        if self.selected_player:
            self._update_player_details()
    
    def _update_player_details(self):
        """Aggiorna i dettagli del giocatore selezionato"""
        if not self.selected_player:
            return
        
        # Pulisce il contenuto esistente
        for widget in self.player_details_label.master.winfo_children():
            if widget != self.player_details_label:
                widget.destroy()
        
        player = self.selected_player
        
        # Crea layout per i dettagli
        details_grid = ttk.Frame(self.player_details_label.master)
        details_grid.pack(fill='x', pady=10)
        
        # Colonna sinistra - Info base
        left_col = ttk.Frame(details_grid)
        left_col.pack(side='left', fill='both', expand=True)
        
        ttk.Label(left_col, text=player['name'], font=FONTS['subtitle']).pack(anchor='w')
        
        basic_info = [
            ("Posizione", player['position']),
            ("Età", f"{player['age']} anni"),
            ("Nazionalità", player['nationality']),
            ("Piede Preferito", player.get('foot', 'Destro'))
        ]
        
        for label, value in basic_info:
            self.create_info_row(left_col, label, value)
        
        # Colonna destra - Valori e contratto
        right_col = ttk.Frame(details_grid)
        right_col.pack(side='right', fill='both', expand=True)
        
        contract_info = [
            ("Valutazione", f"{player['rating']}/100"),
            ("Valore di Mercato", player['value']),
            ("Stipendio", player.get('salary', '€50K/settimana')),
            ("Scadenza Contratto", player['contract'])
        ]
        
        for label, value in contract_info:
            self.create_info_row(right_col, label, value)
        
        # Nasconde il messaggio iniziale
        self.player_details_label.pack_forget()
    
    def _generate_sample_players(self) -> List[Dict]:
        """Genera dati di esempio per i giocatori"""
        sample_players = [
            {"name": "Marco Rossi", "position": "ATT", "age": 28, "nationality": "Italia", "rating": 85, "value": "€8.5M", "contract": "2026"},
            {"name": "Luca Bianchi", "position": "CC", "age": 25, "nationality": "Italia", "rating": 82, "value": "€6.2M", "contract": "2025"},
            {"name": "Giuseppe Verdi", "position": "DIF", "age": 30, "nationality": "Italia", "rating": 80, "value": "€4.8M", "contract": "2024"},
            {"name": "Antonio Neri", "position": "POR", "age": 26, "nationality": "Italia", "rating": 78, "value": "€3.5M", "contract": "2027"},
            {"name": "Francesco Blu", "position": "ATT", "age": 23, "nationality": "Italia", "rating": 77, "value": "€5.1M", "contract": "2026"},
            {"name": "Carlos Silva", "position": "CC", "age": 29, "nationality": "Brasile", "rating": 83, "value": "€7.2M", "contract": "2025"},
            {"name": "Jean Dupont", "position": "DIF", "age": 27, "nationality": "Francia", "rating": 79, "value": "€4.2M", "contract": "2024"},
            {"name": "Hans Mueller", "position": "CC", "age": 24, "nationality": "Germania", "rating": 76, "value": "€3.8M", "contract": "2026"},
            {"name": "Pablo Garcia", "position": "ATT", "age": 22, "nationality": "Spagna", "rating": 74, "value": "€2.9M", "contract": "2027"},
            {"name": "Andrea Gialli", "position": "DIF", "age": 31, "nationality": "Italia", "rating": 75, "value": "€2.1M", "contract": "2024"},
            {"name": "Matteo Viola", "position": "POR", "age": 20, "nationality": "Italia", "rating": 68, "value": "€1.2M", "contract": "2028"},
            {"name": "Roberto Grigi", "position": "CC", "age": 33, "nationality": "Italia", "rating": 72, "value": "€1.5M", "contract": "2024"},
            {"name": "Simone Rosa", "position": "DIF", "age": 19, "nationality": "Italia", "rating": 65, "value": "€800K", "contract": "2029"},
            {"name": "Davide Azzurri", "position": "ATT", "age": 21, "nationality": "Italia", "rating": 70, "value": "€1.8M", "contract": "2027"},
            {"name": "Fabio Marroni", "position": "DIF", "age": 26, "nationality": "Italia", "rating": 73, "value": "€2.5M", "contract": "2025"}
        ]
        
        return sample_players
    
    def _get_players_table_data(self) -> List[List]:
        """Converte i dati dei giocatori per la tabella"""
        table_data = []
        for player in self.players_data:
            table_data.append([
                player['name'],
                player['position'],
                player['age'],
                player['nationality'],
                player['rating'],
                player['value'],
                player['contract']
            ])
        return table_data
