# Specifiche dei File JSON per Gioco Manageriale di Calcio

## 1. Introduzione

Questo documento descrive la struttura e la logica dei file JSON progettati per fungere da database iniziale per un gioco manageriale di calcio. L'obiettivo è fornire una base dati chiara, scalabile e facilmente parsabile, in particolare per un'implementazione con il linguaggio Python.

La struttura è divisa in due categorie principali di file:
1.  **File di Campionato**: Ogni file rappresenta una singola lega nazionale (es. `serie_a.json`, `bundesliga.json`).
2.  **File di Competizione Europea**: Ogni file rappresenta una singola coppa continentale (es. `champions_league.json`).

---

## 2. Struttura dei File di Campionato

Ogni file di campionato (es. `serie_a.json`) contiene tutte le informazioni relative a una specifica lega per una data stagione.

### 2.1 Schema Principale

Il file JSON ha la seguente struttura a livello radice:

| Chiave (Key) | Tipo di Dato | Descrizione |
| :--- | :--- | :--- |
| `nome_campionato` | String | Il nome ufficiale del campionato (es. "Serie A"). |
| `paese` | String | La nazione a cui appartiene il campionato (es. "Italia"). |
| `stagione` | String | La stagione di riferimento (es. "2025/2026"). |
| `livello_competizione`| Integer | Il livello gerarchico del campionato. `1` per la prima divisione, `2` per la seconda, e così via. Essenziale per la logica di promozioni e retrocessioni. |
| `squadre` | Array di Oggetti | Una lista contenente gli oggetti di tutte le squadre che partecipano al campionato. |

### 2.2 Schema dell'Oggetto `squadra`

Questo è l'oggetto più importante e contiene tutti i dati specifici di un singolo club.

| Chiave (Key) | Tipo di Dato | Descrizione e Utilizzo nel Gioco |
| :--- | :--- | :--- |
| `nome` | String | Nome completo del club. Usato come identificatore primario. |
| `citta` | String | Città di appartenenza. Utile per derby, logica dei tifosi e meteo. |
| `stadio` | String | Nome ufficiale dello stadio. |
| `capienza_stadio` | Integer | Numero massimo di spettatori. Impatta direttamente sulle finanze (incassi da biglietti). |
| `reputazione` | Integer | **Scala 1-20**. Parametro chiave che influenza l'attrattiva del club per giocatori e staff, la copertura mediatica e gli sponsor. |
| `formazione_predefinita` | String | Modulo di gioco di default (es. "4-3-3"). Utile per l'IA e per la configurazione iniziale del team. |
| `budget_trasferimenti_eur` | Integer | Valuta iniziale disponibile per il calciomercato. |
| `budget_stipendi_eur` | Integer | Budget settimanale o mensile per gli stipendi dei giocatori e dello staff. |
| `condizione_stadio` | Integer | **Scala 1-10**. Qualità delle infrastrutture dello stadio. Può influenzare gli incassi, il morale dei tifosi e il vantaggio di giocare in casa. |
| `strutture_allenamento` | Integer | **Scala 1-10**. Qualità dei centri di allenamento. Impatta direttamente sullo sviluppo dei giocatori della prima squadra. |
| `strutture_giovanili` | Integer | **Scala 1-10**. Qualità delle strutture per le giovanili. Influenza la qualità e il potenziale dei nuovi talenti generati ("regen"). |
| `reclutamento_giovanile` | Integer | **Scala 1-10**. Efficienza e raggio d'azione della rete di osservatori per i giovani. Determina la probabilità di trovare talenti di alto livello. |
| `ultima_posizione_campionato_precedente` | String | Posizione finale nella stagione precedente. Utile per generare gli obiettivi stagionali dalla dirigenza, articoli di giornale ("preview stagione") e per la logica delle teste di serie. |

---

## 3. Struttura dei File delle Coppe Europee

I file delle competizioni europee (es. `champions_league_squadre_aggiuntive.json`) servono a popolare le coppe con i club provenienti da campionati non principali.

### 3.1 Schema Principale

La struttura è quasi identica a quella dei campionati per mantenere la coerenza.

| Chiave (Key) | Tipo di Dato | Descrizione |
| :--- | :--- | :--- |
| `nome_campionato` | String | Il nome della competizione (es. "UEFA Champions League"). |
| `paese` | String | L'organo di governo (es. "UEFA"). |
| `stagione` | String | La stagione di riferimento (es. "2025/2026"). |
| `livello_competizione`| Integer | Un valore alto (es. `10`) per distinguerla dai campionati nazionali e applicare logiche specifiche. |
| `squadre` | Array di Oggetti | Una lista contenente gli oggetti delle squadre qualificate. |

### 3.2 Schema dell'Oggetto `squadra`

**La struttura dell'oggetto `squadra` è identica a quella descritta nella sezione 2.2 per i campionati.** Questo permette di riutilizzare la stessa classe o struttura dati per rappresentare un club, indipendentemente dalla competizione in cui si trova.

---

## 4. Note per l'Implementazione in Python

Questi file JSON sono pensati per essere il punto di partenza per la creazione di oggetti e classi nel gioco.

### 4.1 Caricamento dei Dati

Il caricamento può essere gestito facilmente con la libreria standard `json` di Python.

```python
import json

def carica_campionato(percorso_file):
    """Carica i dati di un campionato da un file JSON."""
    with open(percorso_file, 'r', encoding='utf-8') as f:
        dati_campionato = json.load(f)
    return dati_campionato

# Esempio di utilizzo
serie_a = carica_campionato('serie_a.json')
prima_squadra = serie_a['squadre']
print(f"Squadra: {prima_squadra['nome']}")
print(f"Budget Trasferimenti: {prima_squadra['budget_trasferimenti_eur']:,} €")
```

### 4.2 Modellazione degli Oggetti

Si raccomanda di creare delle classi Python per mappare questi dati, in modo da poter aggiungere logiche e metodi specifici per il gioco.

```python
class Squadra:
    def __init__(self, dati_squadra):
        self.nome = dati_squadra['nome']
        self.citta = dati_squadra['citta']
        self.reputazione = dati_squadra['reputazione']
        self.budget_trasferimenti = dati_squadra['budget_trasferimenti_eur']
        # ... e così via per tutti gli altri attributi

    def __repr__(self):
        return f"<Squadra: {self.nome}>"

class Campionato:
    def __init__(self, dati_campionato):
        self.nome = dati_campionato['nome_campionato']
        self.paese = dati_campionato['paese']
        self.squadre = [Squadra(s) for s in dati_campionato['squadre']]

    def get_squadra_by_name(self, nome):
        for squadra in self.squadre:
            if squadra.nome == nome:
                return squadra
        return None
```

### 4.3 Gestione delle Competizioni

Un modulo `GestoreCompetizioni` potrebbe caricare tutti i file JSON all'avvio del gioco e popolare le varie competizioni. Le squadre che partecipano a più competizioni (campionato e coppa europea) saranno istanziate una sola volta e poi referenziate nelle diverse liste di partecipanti. Il `nome` del club può agire da chiave univoca per questa gestione.