"""
Football Manager - Staff Generator
Generates realistic staff members for football clubs.
"""

import random
from typing import List, Dict, Tuple
from datetime import date, timedelta

from data.name_generator import NameGenerator
from data.data_loader import DataLoader
from staff.coaching_staff import Coach, CoachingRole, PreferredFormation

class StaffGenerator:
    def __init__(self, data_loader: DataLoader, name_generator: NameGenerator):
        self.data_loader = data_loader
        self.name_generator = name_generator
        
    def generate_coach(self, nationality: str = None, experience_level: str = "medium") -> Coach:
        """Generate a realistic coach with appropriate stats"""
        # If no nationality specified, pick a random UEFA country
        if not nationality:
            uefa_countries = self.data_loader.confederations.get("UEFA", {}).get("countries", [])
            if uefa_countries:
                nationality = random.choice(uefa_countries)
            else:
                nationality = "Italia"  # Default fallback
                
        # Generate name
        first_name, last_name = self.name_generator.generate_coach_name(nationality)
        
        # Generate age (typically 30-70 for coaches)
        age = random.randint(30, 70)
        
        # Create coach
        coach = Coach(first_name, last_name, nationality, age)
        
        # Set experience based on level
        if experience_level == "high":
            coach.experience_years = random.randint(15, 40)
            coach.reputation = random.randint(15, 20)
        elif experience_level == "medium":
            coach.experience_years = random.randint(5, 15)
            coach.reputation = random.randint(10, 15)
        else:  # low experience
            coach.experience_years = random.randint(0, 5)
            coach.reputation = random.randint(1, 10)
            
        # Set attributes based on experience
        base_rating = min(20, 5 + coach.experience_years)
        variance = 5
        
        coach.tactical_knowledge = max(1, min(20, base_rating + random.randint(-variance, variance)))
        coach.player_development = max(1, min(20, base_rating + random.randint(-variance, variance)))
        coach.man_management = max(1, min(20, base_rating + random.randint(-variance, variance)))
        coach.motivation = max(1, min(20, base_rating + random.randint(-variance, variance)))
        coach.adaptability = max(1, min(20, base_rating + random.randint(-variance, variance)))
        coach.youth_development = max(1, min(20, base_rating + random.randint(-variance, variance)))
        
        # Set preferred formation
        formations = list(PreferredFormation)
        coach.preferred_formation = random.choice(formations)
        
        # Set role
        coach.role = CoachingRole.HEAD_COACH
        
        return coach
        
    def generate_assistant_coach(self, head_coach_nationality: str = None) -> Coach:
        """Generate an assistant coach that complements the head coach"""
        # Generate name
        if head_coach_nationality:
            first_name, last_name = self.name_generator.generate_coach_name(head_coach_nationality)
            nationality = head_coach_nationality
        else:
            uefa_countries = self.data_loader.confederations.get("UEFA", {}).get("countries", [])
            if uefa_countries:
                nationality = random.choice(uefa_countries)
            else:
                nationality = "Italia"
            first_name, last_name = self.name_generator.generate_coach_name(nationality)
            
        # Generate age (typically 25-60 for assistant coaches)
        age = random.randint(25, 60)
        
        # Create coach
        coach = Coach(first_name, last_name, nationality, age)
        coach.role = CoachingRole.ASSISTANT
        
        # Assistant coaches typically have slightly lower experience
        coach.experience_years = random.randint(2, 20)
        coach.reputation = random.randint(1, 12)
        
        # Set attributes with focus on specific areas
        # Assistant coaches often specialize in one area
        specialization = random.choice([
            "tactical_knowledge", "player_development", "man_management", 
            "motivation", "adaptability", "youth_development"
        ])
        
        # Set specialized attribute higher
        setattr(coach, specialization, random.randint(12, 20))
        
        # Set other attributes
        for attr in ["tactical_knowledge", "player_development", "man_management", 
                    "motivation", "adaptability", "youth_development"]:
            if attr != specialization:
                setattr(coach, attr, random.randint(8, 16))
                
        # Set preferred formation
        formations = list(PreferredFormation)
        coach.preferred_formation = random.choice(formations)
        
        return coach
        
    def generate_staff_team(self, club_reputation: int) -> List[Coach]:
        """Generate complete staff team for a club based on its reputation"""
        staff_team = []
        
        # Higher reputation clubs have more staff
        if club_reputation >= 18:
            num_assistants = random.randint(3, 5)
            has_specialists = True
        elif club_reputation >= 15:
            num_assistants = random.randint(2, 4)
            has_specialists = True
        elif club_reputation >= 12:
            num_assistants = random.randint(1, 3)
            has_specialists = random.random() > 0.5
        else:
            num_assistants = random.randint(0, 2)
            has_specialists = False
            
        # Generate head coach
        experience_level = "high" if club_reputation >= 17 else "medium" if club_reputation >= 14 else "low"
        head_coach = self.generate_coach(experience_level=experience_level)
        staff_team.append(head_coach)
        
        # Generate assistant coaches
        for _ in range(num_assistants):
            assistant = self.generate_assistant_coach(head_coach.nationality)
            staff_team.append(assistant)
            
        # Generate specialists if club can afford them
        if has_specialists:
            # Goalkeeping coach
            gk_coach = self._generate_specialist(CoachingRole.GOALKEEPING, head_coach.nationality)
            staff_team.append(gk_coach)
            
            # Fitness coach
            fitness_coach = self._generate_specialist(CoachingRole.FITNESS, head_coach.nationality)
            staff_team.append(fitness_coach)
            
            # If very high reputation, add more specialists
            if club_reputation >= 18:
                # Chief scout
                chief_scout = self._generate_specialist(CoachingRole.CHIEF_SCOUT, head_coach.nationality)
                staff_team.append(chief_scout)
                
                # Medical staff
                medical_staff = self._generate_specialist(CoachingRole.MEDICAL, head_coach.nationality)
                staff_team.append(medical_staff)
                
        return staff_team
        
    def _generate_specialist(self, role: CoachingRole, nationality: str) -> Coach:
        """Generate a specialist staff member"""
        first_name, last_name = self.name_generator.generate_staff_name(nationality, role.value)
        age = random.randint(25, 65)
        
        coach = Coach(first_name, last_name, nationality, age)
        coach.role = role
        
        # Specialists have focused expertise
        coach.experience_years = random.randint(5, 25)
        coach.reputation = random.randint(5, 15)
        
        # Set high rating in their specialty
        specialty_attr = self._get_specialty_attribute(role)
        setattr(coach, specialty_attr, random.randint(15, 20))
        
        # Set moderate ratings in other areas
        for attr in ["tactical_knowledge", "player_development", "man_management", 
                    "motivation", "adaptability", "youth_development"]:
            if attr != specialty_attr:
                setattr(coach, attr, random.randint(8, 15))
                
        return coach
        
    def _get_specialty_attribute(self, role: CoachingRole) -> str:
        """Map coaching role to specialty attribute"""
        mapping = {
            CoachingRole.GOALKEEPING: "player_development",
            CoachingRole.FITNESS: "motivation",
            CoachingRole.SCOUT: "player_development",
            CoachingRole.CHIEF_SCOUT: "player_development",
            CoachingRole.MEDICAL: "man_management",
            CoachingRole.PHYSIOTHERAPIST: "man_management",
            CoachingRole.PSYCHOLOGIST: "motivation",
            CoachingRole.NUTRITIONIST: "motivation",
            CoachingRole.TECHNICAL_DIRECTOR: "tactical_knowledge",
            CoachingRole.YOUTH_COORDINATOR: "youth_development"
        }
        return mapping.get(role, "tactical_knowledge")
        
    def generate_contract_for_staff(self, staff_member: Coach, club: 'Club') -> 'CoachContract':
        """Generate a contract for a staff member based on club and staff reputation"""
        from datetime import date, timedelta
        
        # Contract length based on reputation
        if staff_member.reputation >= 18:
            contract_length_years = random.randint(3, 5)
        elif staff_member.reputation >= 15:
            contract_length_years = random.randint(2, 4)
        else:
            contract_length_years = random.randint(1, 3)
            
        start_date = date.today()
        end_date = date(start_date.year + contract_length_years, start_date.month, start_date.day)
        
        # Salary based on reputation and club budget
        base_salary = staff_member.reputation * 50000
        club_factor = min(2.0, max(0.5, club.reputation.overall / 10))
        salary = int(base_salary * club_factor)
        
        # Add bonuses for high reputation staff
        bonuses = {}
        if staff_member.reputation >= 17:
            bonuses["win_bonus"] = 5000
            bonuses["title_bonus"] = 50000
            
        from .coaching_staff import CoachContract
        contract = CoachContract(
            start_date=start_date,
            end_date=end_date,
            salary=salary,
            bonuses=bonuses
        )
        
        return contract