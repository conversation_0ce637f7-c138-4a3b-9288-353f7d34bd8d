"""
Interfaccia grafica principale del Football Manager
Punto di ingresso per l'interfaccia utente
"""

import tkinter as tk
from tkinter import ttk
import sys
from pathlib import Path

# Aggiungi il percorso src per gli import
src_path = Path(__file__).parent.parent
sys.path.insert(0, str(src_path))

from interface.base_window import BaseWindow
from interface.gui_config import WINDOW_WIDTH, WINDOW_HEIGHT, MIN_WINDOW_WIDTH, MIN_WINDOW_HEIGHT
from interface.main_menu import MainMenu
from interface.club_selection import ClubSelectionWindow
from interface.dashboard import GameDashboard
from data.data_loader import DataLoader
from game.game_engine import GameEngine


class FootballManagerGUI:
    """Classe principale per la gestione dell'interfaccia grafica"""
    
    def __init__(self):
        self.root = None
        self.current_window = None
        self.data_loader = None
        self.game_engine = None
        self.selected_club = None
        
        # Inizializza i componenti del gioco
        self._initialize_game_components()
    
    def _initialize_game_components(self):
        """Inizializza i componenti del motore di gioco"""
        try:
            # Carica i dati del gioco
            self.data_loader = DataLoader()
            if not self.data_loader.load_all_data():
                raise Exception("Impossibile caricare i dati del gioco")
            
            # Inizializza il motore di gioco
            self.game_engine = GameEngine()
            
            print("Componenti del gioco inizializzati con successo")
            
        except Exception as e:
            print(f"Errore nell'inizializzazione: {e}")
            self.data_loader = None
            self.game_engine = None
    
    def start(self):
        """Avvia l'interfaccia grafica"""
        # Crea la finestra principale
        self.root = tk.Tk()
        self.root.withdraw()  # Nasconde la finestra root
        
        # Configura la finestra principale
        self._configure_root()
        
        # Mostra il menu principale
        self.show_main_menu()
        
        # Avvia il loop principale
        self.root.mainloop()
    
    def _configure_root(self):
        """Configura la finestra root principale"""
        self.root.title("Football Manager - Edizione Presidenziale")
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        self.root.minsize(MIN_WINDOW_WIDTH, MIN_WINDOW_HEIGHT)
        
        # Centra la finestra
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - WINDOW_WIDTH) // 2
        y = (screen_height - WINDOW_HEIGHT) // 2
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}+{x}+{y}")
    
    def show_main_menu(self):
        """Mostra il menu principale"""
        if self.current_window:
            self.current_window.close()
        
        self.current_window = MainMenu(self.root)
        
        # Configura i callback del menu principale
        self.current_window.add_callback('new_game', self.start_new_game)
        self.current_window.add_callback('load_game', self.load_game)
        self.current_window.add_callback('settings', self.show_settings)
        self.current_window.add_callback('exit', self.exit_application)
        
        self.current_window.show()
    
    def start_new_game(self):
        """Avvia un nuovo gioco"""
        if not self.data_loader:
            self._show_error("Errore", "Dati del gioco non disponibili. Riavviare l'applicazione.")
            return
        
        # Mostra la selezione del club
        self.show_club_selection()
    
    def show_club_selection(self):
        """Mostra la finestra di selezione del club"""
        if self.current_window:
            self.current_window.close()
        
        self.current_window = ClubSelectionWindow(self.root, self.data_loader)
        
        # Configura i callback
        self.current_window.add_callback('club_selected', self.on_club_selected)
        self.current_window.add_callback('back_to_menu', self.show_main_menu)
        
        self.current_window.show()
    
    def on_club_selected(self, club_data):
        """Gestisce la selezione di un club"""
        self.selected_club = club_data
        
        # Inizializza il gioco con il club selezionato
        if self.game_engine:
            self.game_engine.initialize_game(club_data['nome'])
        
        # Mostra la dashboard del gioco
        self.show_game_dashboard()
    
    def show_game_dashboard(self):
        """Mostra la dashboard principale del gioco"""
        if self.current_window:
            self.current_window.close()
        
        self.current_window = GameDashboard(
            self.root, 
            self.selected_club, 
            self.game_engine, 
            self.data_loader
        )
        
        # Configura i callback
        self.current_window.add_callback('back_to_menu', self.show_main_menu)
        self.current_window.add_callback('save_game', self.save_game)
        
        self.current_window.show()
    
    def load_game(self):
        """Carica una partita salvata"""
        # TODO: Implementare il caricamento delle partite
        self._show_info("Carica Partita", "Funzionalità in sviluppo")
    
    def save_game(self):
        """Salva la partita corrente"""
        if self.game_engine:
            success = self.game_engine.save_game("current_save")
            if success:
                self._show_info("Salvataggio", "Partita salvata con successo")
            else:
                self._show_error("Errore", "Impossibile salvare la partita")
    
    def show_settings(self):
        """Mostra le impostazioni"""
        # TODO: Implementare le impostazioni
        self._show_info("Impostazioni", "Funzionalità in sviluppo")
    
    def exit_application(self):
        """Esce dall'applicazione"""
        if self._confirm_exit():
            self.root.quit()
    
    def _confirm_exit(self) -> bool:
        """Chiede conferma prima di uscire"""
        from tkinter import messagebox
        return messagebox.askyesno(
            "Conferma Uscita", 
            "Sei sicuro di voler uscire dal gioco?"
        )
    
    def _show_info(self, title: str, message: str):
        """Mostra un messaggio informativo"""
        from tkinter import messagebox
        messagebox.showinfo(title, message)
    
    def _show_error(self, title: str, message: str):
        """Mostra un messaggio di errore"""
        from tkinter import messagebox
        messagebox.showerror(title, message)


def main():
    """Funzione principale per avviare l'interfaccia grafica"""
    try:
        app = FootballManagerGUI()
        app.start()
    except Exception as e:
        print(f"Errore critico nell'avvio dell'applicazione: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
