"""
Menu Mercato Trasferimenti
Gestisce acquisti, vendite e prestiti di giocatori
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List
from .base_menu import BaseMenu
from ..gui_config import COLORS, FONTS, ICONS


class TransfersMenu(BaseMenu):
    """Menu per la gestione del mercato trasferimenti"""
    
    def setup_ui(self):
        """Configura l'interfaccia del menu trasferimenti"""
        # Header
        self.create_section_header(self.main_frame, "Mercato Trasferimenti", 
                                 "Acquisti, vendite e prestiti")
        
        # Crea le schede per diverse sezioni del mercato
        tabs_config = [
            {'text': 'Ricerca Giocatori', 'setup_func': self._setup_search_tab},
            {'text': 'Trattative', 'setup_func': self._setup_negotiations_tab},
            {'text': 'Lista Cessioni', 'setup_func': self._setup_sales_tab},
            {'text': 'Prestiti', 'setup_func': self._setup_loans_tab}
        ]
        
        self.notebook = self.create_tabs(self.main_frame, tabs_config)
    
    def _setup_search_tab(self, parent: tk.Widget):
        """Configura la scheda ricerca giocatori"""
        # Filtri di ricerca
        search_frame = ttk.LabelFrame(parent, text="Filtri di Ricerca", padding=15)
        search_frame.pack(fill='x', pady=(0, 10))
        
        # Prima riga filtri
        filters_row1 = ttk.Frame(search_frame)
        filters_row1.pack(fill='x', pady=(0, 10))
        
        # Posizione
        ttk.Label(filters_row1, text="Posizione:").pack(side='left', padx=(0, 5))
        position_combo = ttk.Combobox(filters_row1, values=['Tutte', 'POR', 'DIF', 'CC', 'ATT'], 
                                    state='readonly', width=8)
        position_combo.set('Tutte')
        position_combo.pack(side='left', padx=(0, 20))
        
        # Età
        ttk.Label(filters_row1, text="Età:").pack(side='left', padx=(0, 5))
        age_from = ttk.Spinbox(filters_row1, from_=16, to=40, width=5)
        age_from.set("18")
        age_from.pack(side='left', padx=(0, 5))
        ttk.Label(filters_row1, text="-").pack(side='left', padx=(0, 5))
        age_to = ttk.Spinbox(filters_row1, from_=16, to=40, width=5)
        age_to.set("35")
        age_to.pack(side='left', padx=(0, 20))
        
        # Nazionalità
        ttk.Label(filters_row1, text="Nazionalità:").pack(side='left', padx=(0, 5))
        nationality_combo = ttk.Combobox(filters_row1, values=['Tutte', 'Italia', 'Brasile', 'Argentina', 'Francia'], 
                                       state='readonly', width=12)
        nationality_combo.set('Tutte')
        nationality_combo.pack(side='left')
        
        # Seconda riga filtri
        filters_row2 = ttk.Frame(search_frame)
        filters_row2.pack(fill='x')
        
        # Valore
        ttk.Label(filters_row2, text="Valore (€):").pack(side='left', padx=(0, 5))
        value_from = ttk.Entry(filters_row2, width=10)
        value_from.insert(0, "0")
        value_from.pack(side='left', padx=(0, 5))
        ttk.Label(filters_row2, text="-").pack(side='left', padx=(0, 5))
        value_to = ttk.Entry(filters_row2, width=10)
        value_to.insert(0, "50000000")
        value_to.pack(side='left', padx=(0, 20))
        
        # Pulsante ricerca
        search_btn = ttk.Button(filters_row2, text="🔍 Cerca", style='Primary.TButton')
        search_btn.pack(side='left')
        
        # Risultati ricerca
        results_frame = ttk.LabelFrame(parent, text="Risultati Ricerca", padding=10)
        results_frame.pack(fill='both', expand=True)
        
        # Tabella giocatori disponibili
        search_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'age', 'text': 'Età', 'width': 40},
            {'id': 'position', 'text': 'Pos', 'width': 40},
            {'id': 'nationality', 'text': 'Nazionalità', 'width': 80},
            {'id': 'club', 'text': 'Club', 'width': 100},
            {'id': 'value', 'text': 'Valore', 'width': 80},
            {'id': 'rating', 'text': 'Val', 'width': 40},
            {'id': 'status', 'text': 'Stato', 'width': 80}
        ]
        
        search_data = [
            ["Carlos Santos", "24", "ATT", "Brasile", "Flamengo", "€12M", "84", "Disponibile"],
            ["Jean Moreau", "26", "CC", "Francia", "Lyon", "€8M", "81", "Disponibile"],
            ["Hans Weber", "22", "DIF", "Germania", "Dortmund", "€15M", "79", "Interessato"],
            ["Pablo Ruiz", "28", "CC", "Spagna", "Valencia", "€6M", "83", "Disponibile"],
            ["Andrea Conti", "25", "DIF", "Italia", "Atalanta", "€10M", "80", "Trattativa"]
        ]
        
        search_tree = self.create_data_table(results_frame, search_columns, search_data, height=10)
        
        # Pulsanti azioni
        search_buttons = [
            {'text': '👁️ Osserva', 'command': self._scout_player, 'style': 'Secondary.TButton'},
            {'text': '💰 Fai Offerta', 'command': self._make_offer, 'style': 'Primary.TButton'},
            {'text': '📋 Dettagli', 'command': self._player_details, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(results_frame, search_buttons)
    
    def _setup_negotiations_tab(self, parent: tk.Widget):
        """Configura la scheda trattative"""
        # Trattative in corso
        active_frame = ttk.LabelFrame(parent, text="Trattative in Corso", padding=15)
        active_frame.pack(fill='x', pady=(0, 10))
        
        negotiations_columns = [
            {'id': 'player', 'text': 'Giocatore', 'width': 120},
            {'id': 'club', 'text': 'Club', 'width': 100},
            {'id': 'type', 'text': 'Tipo', 'width': 80},
            {'id': 'our_offer', 'text': 'Nostra Offerta', 'width': 100},
            {'id': 'their_demand', 'text': 'Richiesta', 'width': 100},
            {'id': 'status', 'text': 'Stato', 'width': 100},
            {'id': 'deadline', 'text': 'Scadenza', 'width': 80}
        ]
        
        negotiations_data = [
            ["Carlos Santos", "Flamengo", "Acquisto", "€10M", "€12M", "In trattativa", "3 giorni"],
            ["Marco Belli", "Genoa", "Vendita", "€5M", "€4M", "Rifiutata", "5 giorni"],
            ["Luis Garcia", "Sevilla", "Prestito", "€500K", "€800K", "Controfferta", "2 giorni"]
        ]
        
        negotiations_tree = self.create_data_table(active_frame, negotiations_columns, negotiations_data, height=6)
        
        # Pulsanti trattative
        negotiations_buttons = [
            {'text': '💬 Negozia', 'command': self._negotiate, 'style': 'Primary.TButton'},
            {'text': '📞 Chiama Agente', 'command': self._call_agent, 'style': 'Secondary.TButton'},
            {'text': '❌ Ritira Offerta', 'command': self._withdraw_offer, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(active_frame, negotiations_buttons)
        
        # Storico trattative
        history_frame = ttk.LabelFrame(parent, text="Storico Trattative", padding=15)
        history_frame.pack(fill='both', expand=True)
        
        history_columns = [
            {'id': 'date', 'text': 'Data', 'width': 80},
            {'id': 'player', 'text': 'Giocatore', 'width': 120},
            {'id': 'club', 'text': 'Club', 'width': 100},
            {'id': 'type', 'text': 'Tipo', 'width': 80},
            {'id': 'amount', 'text': 'Importo', 'width': 100},
            {'id': 'result', 'text': 'Risultato', 'width': 100}
        ]
        
        history_data = [
            ["10/01", "Roberto Mancini", "Sampdoria", "Acquisto", "€8M", "Completato"],
            ["05/01", "Fabio Rossi", "Bologna", "Vendita", "€3M", "Completato"],
            ["28/12", "Diego Silva", "Santos", "Prestito", "€1M", "Fallito"],
            ["20/12", "Andrea Pirlo", "Brescia", "Acquisto", "€15M", "Rifiutato"]
        ]
        
        self.create_data_table(history_frame, history_columns, history_data, height=8)
    
    def _setup_sales_tab(self, parent: tk.Widget):
        """Configura la scheda lista cessioni"""
        # Giocatori in vendita
        sales_frame = ttk.LabelFrame(parent, text="Giocatori in Vendita", padding=15)
        sales_frame.pack(fill='x', pady=(0, 10))
        
        sales_columns = [
            {'id': 'player', 'text': 'Giocatore', 'width': 120},
            {'id': 'position', 'text': 'Pos', 'width': 40},
            {'id': 'age', 'text': 'Età', 'width': 40},
            {'id': 'rating', 'text': 'Val', 'width': 40},
            {'id': 'asking_price', 'text': 'Prezzo Richiesto', 'width': 100},
            {'id': 'market_value', 'text': 'Valore Mercato', 'width': 100},
            {'id': 'interest', 'text': 'Interesse', 'width': 80},
            {'id': 'status', 'text': 'Stato', 'width': 80}
        ]
        
        sales_data = [
            ["Giovanni Rossi", "CC", "29", "75", "€4M", "€3.5M", "Alto", "In vendita"],
            ["Marco Bianchi", "DIF", "31", "73", "€2M", "€1.8M", "Medio", "In vendita"],
            ["Luca Verdi", "ATT", "26", "71", "€3M", "€2.5M", "Basso", "In vendita"]
        ]
        
        sales_tree = self.create_data_table(sales_frame, sales_columns, sales_data, height=6)
        
        # Pulsanti gestione vendite
        sales_buttons = [
            {'text': '➕ Aggiungi alla Lista', 'command': self._add_to_sales, 'style': 'Primary.TButton'},
            {'text': '💰 Modifica Prezzo', 'command': self._modify_price, 'style': 'Secondary.TButton'},
            {'text': '➖ Rimuovi dalla Lista', 'command': self._remove_from_sales, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(sales_frame, sales_buttons)
        
        # Offerte ricevute
        offers_frame = ttk.LabelFrame(parent, text="Offerte Ricevute", padding=15)
        offers_frame.pack(fill='both', expand=True)
        
        offers_columns = [
            {'id': 'date', 'text': 'Data', 'width': 80},
            {'id': 'club', 'text': 'Club', 'width': 100},
            {'id': 'player', 'text': 'Giocatore', 'width': 120},
            {'id': 'offer', 'text': 'Offerta', 'width': 100},
            {'id': 'our_price', 'text': 'Nostro Prezzo', 'width': 100},
            {'id': 'status', 'text': 'Stato', 'width': 100}
        ]
        
        offers_data = [
            ["Oggi", "Milan", "Giovanni Rossi", "€3.5M", "€4M", "In attesa"],
            ["Ieri", "Fiorentina", "Marco Bianchi", "€1.5M", "€2M", "Rifiutata"],
            ["2 giorni fa", "Torino", "Luca Verdi", "€2.8M", "€3M", "In valutazione"]
        ]
        
        offers_tree = self.create_data_table(offers_frame, offers_columns, offers_data, height=6)
        
        # Pulsanti offerte
        offers_buttons = [
            {'text': '✅ Accetta', 'command': self._accept_offer, 'style': 'Primary.TButton'},
            {'text': '❌ Rifiuta', 'command': self._reject_offer, 'style': 'Secondary.TButton'},
            {'text': '💬 Controfferta', 'command': self._counter_offer, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(offers_frame, offers_buttons)
    
    def _setup_loans_tab(self, parent: tk.Widget):
        """Configura la scheda prestiti"""
        # Prestiti in uscita
        loans_out_frame = ttk.LabelFrame(parent, text="Prestiti in Uscita", padding=15)
        loans_out_frame.pack(fill='x', pady=(0, 10))
        
        loans_out_columns = [
            {'id': 'player', 'text': 'Giocatore', 'width': 120},
            {'id': 'club', 'text': 'Club', 'width': 100},
            {'id': 'start_date', 'text': 'Inizio', 'width': 80},
            {'id': 'end_date', 'text': 'Fine', 'width': 80},
            {'id': 'fee', 'text': 'Prestito', 'width': 80},
            {'id': 'buy_option', 'text': 'Riscatto', 'width': 80},
            {'id': 'performances', 'text': 'Prestazioni', 'width': 100}
        ]
        
        loans_out_data = [
            ["Matteo Gialli", "Empoli", "01/07/24", "30/06/25", "€200K", "€2M", "8 gol, 15 presenze"],
            ["Simone Blu", "Spezia", "01/07/24", "30/06/25", "€150K", "No", "3 assist, 12 presenze"]
        ]
        
        self.create_data_table(loans_out_frame, loans_out_columns, loans_out_data, height=4)
        
        # Prestiti in entrata
        loans_in_frame = ttk.LabelFrame(parent, text="Prestiti in Entrata", padding=15)
        loans_in_frame.pack(fill='x', pady=10)
        
        loans_in_columns = [
            {'id': 'player', 'text': 'Giocatore', 'width': 120},
            {'id': 'club', 'text': 'Club Proprietario', 'width': 120},
            {'id': 'start_date', 'text': 'Inizio', 'width': 80},
            {'id': 'end_date', 'text': 'Fine', 'width': 80},
            {'id': 'fee', 'text': 'Prestito', 'width': 80},
            {'id': 'buy_option', 'text': 'Riscatto', 'width': 80},
            {'id': 'performances', 'text': 'Prestazioni', 'width': 100}
        ]
        
        loans_in_data = [
            ["Pedro Martinez", "Real Madrid", "01/01/25", "30/06/25", "€500K", "€8M", "2 gol, 8 presenze"]
        ]
        
        self.create_data_table(loans_in_frame, loans_in_columns, loans_in_data, height=4)
        
        # Opportunità prestiti
        opportunities_frame = ttk.LabelFrame(parent, text="Opportunità Prestiti", padding=15)
        opportunities_frame.pack(fill='both', expand=True)
        
        opportunities_columns = [
            {'id': 'player', 'text': 'Giocatore', 'width': 120},
            {'id': 'club', 'text': 'Club', 'width': 100},
            {'id': 'position', 'text': 'Pos', 'width': 40},
            {'id': 'age', 'text': 'Età', 'width': 40},
            {'id': 'rating', 'text': 'Val', 'width': 40},
            {'id': 'loan_fee', 'text': 'Prestito', 'width': 80},
            {'id': 'buy_option', 'text': 'Riscatto', 'width': 80},
            {'id': 'availability', 'text': 'Disponibilità', 'width': 100}
        ]
        
        opportunities_data = [
            ["Alex Johnson", "Chelsea", "ATT", "20", "76", "€300K", "€5M", "Disponibile"],
            ["Marco Silva", "Porto", "CC", "23", "78", "€400K", "€6M", "Interessato"],
            ["Yuki Tanaka", "Tokyo FC", "DIF", "21", "74", "€200K", "€3M", "Disponibile"]
        ]
        
        self.create_data_table(opportunities_frame, opportunities_columns, opportunities_data, height=6)
        
        # Pulsanti prestiti
        loans_buttons = [
            {'text': '📋 Richiedi Prestito', 'command': self._request_loan, 'style': 'Primary.TButton'},
            {'text': '🔄 Rinnova Prestito', 'command': self._renew_loan, 'style': 'Secondary.TButton'},
            {'text': '💰 Esercita Riscatto', 'command': self._exercise_option, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(opportunities_frame, loans_buttons)
    
    # Metodi placeholder per le azioni
    def _scout_player(self):
        self.show_message("Osserva Giocatore", "Funzionalità in sviluppo", "info")
    
    def _make_offer(self):
        self.show_message("Fai Offerta", "Funzionalità in sviluppo", "info")
    
    def _player_details(self):
        self.show_message("Dettagli Giocatore", "Funzionalità in sviluppo", "info")
    
    def _negotiate(self):
        self.show_message("Negozia", "Funzionalità in sviluppo", "info")
    
    def _call_agent(self):
        self.show_message("Chiama Agente", "Funzionalità in sviluppo", "info")
    
    def _withdraw_offer(self):
        self.show_message("Ritira Offerta", "Funzionalità in sviluppo", "info")
    
    def _add_to_sales(self):
        self.show_message("Aggiungi alla Lista", "Funzionalità in sviluppo", "info")
    
    def _modify_price(self):
        self.show_message("Modifica Prezzo", "Funzionalità in sviluppo", "info")
    
    def _remove_from_sales(self):
        self.show_message("Rimuovi dalla Lista", "Funzionalità in sviluppo", "info")
    
    def _accept_offer(self):
        self.show_message("Accetta Offerta", "Funzionalità in sviluppo", "info")
    
    def _reject_offer(self):
        self.show_message("Rifiuta Offerta", "Funzionalità in sviluppo", "info")
    
    def _counter_offer(self):
        self.show_message("Controfferta", "Funzionalità in sviluppo", "info")
    
    def _request_loan(self):
        self.show_message("Richiedi Prestito", "Funzionalità in sviluppo", "info")
    
    def _renew_loan(self):
        self.show_message("Rinnova Prestito", "Funzionalità in sviluppo", "info")
    
    def _exercise_option(self):
        self.show_message("Esercita Riscatto", "Funzionalità in sviluppo", "info")
