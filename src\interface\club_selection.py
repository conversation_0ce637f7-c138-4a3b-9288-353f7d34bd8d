"""
Schermata di selezione del club
Permette al giocatore di scegliere il club da gestire con filtri e informazioni dettagliate
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Optional
from .base_window import BaseWindow
from .gui_config import COLORS, FONTS, COMPONENT_SIZES, TEXTS, ICONS
from data.data_loader import DataLoader
from data.name_generator import NameGenerator


class ClubSelectionWindow(BaseWindow):
    """Finestra per la selezione del club"""
    
    def __init__(self, parent: tk.Widget, data_loader: DataLoader):
        super().__init__(parent, TEXTS['club_selection']['title'])
        self.data_loader = data_loader
        self.name_generator = NameGenerator(data_loader)
        
        # Dati dei club con presidenti generati
        self.clubs_data = []
        self.filtered_clubs = []
        self.selected_club = None
        
        # Variabili per i filtri
        self.country_var = tk.StringVar()
        self.league_var = tk.StringVar()
        self.reputation_var = tk.StringVar()
        
        # Widget references
        self.clubs_tree = None
        self.info_frame = None
        
        # Carica e prepara i dati dei club
        self._load_clubs_data()
    
    def _load_clubs_data(self):
        """Carica i dati dei club e genera i presidenti"""
        all_clubs = self.data_loader.get_all_clubs()
        
        for club_data in all_clubs:
            # Genera nome del presidente
            country = club_data.get('paese', 'Italia')
            first_name, last_name = self.name_generator.generate_board_member_name(country)
            
            # Crea una copia dei dati del club con informazioni aggiuntive
            enhanced_club = club_data.copy()
            enhanced_club['presidente'] = f"{first_name} {last_name}"
            enhanced_club['paese'] = country
            
            # Determina il campionato dal file di origine
            enhanced_club['campionato'] = self._determine_league_name(club_data)
            
            self.clubs_data.append(enhanced_club)
        
        self.filtered_clubs = self.clubs_data.copy()
    
    def _determine_league_name(self, club_data: Dict) -> str:
        """Determina il nome del campionato basandosi sui dati"""
        # Cerca il club nei dati delle leghe per determinare il campionato
        for country, leagues in self.data_loader.leagues_data.items():
            for league_name, league_data in leagues.items():
                if 'squadre' in league_data:
                    for club in league_data['squadre']:
                        if club['nome'] == club_data['nome']:
                            return league_data.get('nome_campionato', league_name)
        return 'Sconosciuto'
    
    def setup_ui(self):
        """Configura l'interfaccia utente"""
        if not self.window:
            return
        
        # Frame principale
        main_frame = ttk.Frame(self.window, style='Main.TFrame')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Header
        self.create_header(main_frame, TEXTS['club_selection']['title'], 
                          TEXTS['club_selection']['choose_club'])
        
        # Frame per filtri e lista
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill='both', expand=True, pady=10)
        
        # Crea i filtri
        self._create_filters_section(content_frame)
        
        # Frame per lista club e dettagli
        clubs_frame = ttk.Frame(content_frame)
        clubs_frame.pack(fill='both', expand=True, pady=10)
        
        # Lista dei club (sinistra)
        self._create_clubs_list(clubs_frame)
        
        # Dettagli del club (destra)
        self._create_club_details(clubs_frame)
        
        # Pulsanti di controllo
        self._create_control_buttons(main_frame)
    
    def _create_filters_section(self, parent: tk.Widget):
        """Crea la sezione dei filtri"""
        filters_frame = ttk.LabelFrame(parent, text="Filtri", padding=10)
        filters_frame.pack(fill='x', pady=(0, 10))
        
        # Frame per organizzare i filtri in colonne
        filters_grid = ttk.Frame(filters_frame)
        filters_grid.pack(fill='x')
        
        # Filtro paese
        country_frame = ttk.Frame(filters_grid)
        country_frame.pack(side='left', padx=(0, 20))
        
        ttk.Label(country_frame, text=TEXTS['club_selection']['country']).pack(anchor='w')
        country_combo = ttk.Combobox(country_frame, textvariable=self.country_var, 
                                   state='readonly', width=15)
        country_combo.pack()
        
        # Popola i paesi
        countries = sorted(set(club['paese'] for club in self.clubs_data))
        country_combo['values'] = ['Tutti'] + countries
        country_combo.set('Tutti')
        country_combo.bind('<<ComboboxSelected>>', self._on_filter_changed)
        
        # Filtro campionato
        league_frame = ttk.Frame(filters_grid)
        league_frame.pack(side='left', padx=(0, 20))
        
        ttk.Label(league_frame, text=TEXTS['club_selection']['league']).pack(anchor='w')
        league_combo = ttk.Combobox(league_frame, textvariable=self.league_var, 
                                  state='readonly', width=20)
        league_combo.pack()
        
        # Popola i campionati
        leagues = sorted(set(club['campionato'] for club in self.clubs_data))
        league_combo['values'] = ['Tutti'] + leagues
        league_combo.set('Tutti')
        league_combo.bind('<<ComboboxSelected>>', self._on_filter_changed)
        
        # Filtro reputazione
        reputation_frame = ttk.Frame(filters_grid)
        reputation_frame.pack(side='left')
        
        ttk.Label(reputation_frame, text=TEXTS['club_selection']['reputation']).pack(anchor='w')
        reputation_combo = ttk.Combobox(reputation_frame, textvariable=self.reputation_var, 
                                      state='readonly', width=15)
        reputation_combo.pack()
        
        reputation_combo['values'] = ['Tutte', 'Alta (15+)', 'Media (10-14)', 'Bassa (<10)']
        reputation_combo.set('Tutte')
        reputation_combo.bind('<<ComboboxSelected>>', self._on_filter_changed)
    
    def _create_clubs_list(self, parent: tk.Widget):
        """Crea la lista dei club"""
        # Frame per la lista
        list_frame = ttk.Frame(parent)
        list_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # Etichetta
        ttk.Label(list_frame, text="Club Disponibili", font=FONTS['subtitle']).pack(anchor='w')
        
        # Treeview per la lista dei club
        columns = ('nome', 'citta', 'campionato', 'reputazione', 'budget')
        self.clubs_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configura le colonne
        self.clubs_tree.heading('nome', text='Nome')
        self.clubs_tree.heading('citta', text='Città')
        self.clubs_tree.heading('campionato', text='Campionato')
        self.clubs_tree.heading('reputazione', text='Rep.')
        self.clubs_tree.heading('budget', text='Budget (M€)')
        
        self.clubs_tree.column('nome', width=150)
        self.clubs_tree.column('citta', width=100)
        self.clubs_tree.column('campionato', width=120)
        self.clubs_tree.column('reputazione', width=50)
        self.clubs_tree.column('budget', width=80)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.clubs_tree.yview)
        self.clubs_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview e scrollbar
        self.clubs_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Bind selezione
        self.clubs_tree.bind('<<TreeviewSelect>>', self._on_club_selected)
        
        # Popola la lista iniziale
        self._populate_clubs_list()
    
    def _create_club_details(self, parent: tk.Widget):
        """Crea la sezione dei dettagli del club"""
        # Frame per i dettagli
        self.info_frame = ttk.LabelFrame(parent, text="Dettagli Club", padding=15)
        self.info_frame.pack(side='right', fill='both', expand=False, padx=(10, 0))
        
        # Configura larghezza fissa
        self.info_frame.configure(width=350)
        self.info_frame.pack_propagate(False)
        
        # Messaggio iniziale
        initial_label = ttk.Label(self.info_frame, 
                                text="Seleziona un club per vedere i dettagli",
                                font=FONTS['normal'],
                                foreground=COLORS['text_secondary'])
        initial_label.pack(expand=True)
    
    def _populate_clubs_list(self):
        """Popola la lista dei club"""
        # Pulisce la lista esistente
        for item in self.clubs_tree.get_children():
            self.clubs_tree.delete(item)
        
        # Aggiunge i club filtrati
        for club in self.filtered_clubs:
            budget_millions = club['budget_trasferimenti_eur'] / 1_000_000
            
            self.clubs_tree.insert('', 'end', values=(
                club['nome'],
                club['citta'],
                club['campionato'],
                club['reputazione'],
                f"{budget_millions:.1f}"
            ))
    
    def _on_filter_changed(self, event=None):
        """Gestisce il cambio dei filtri"""
        self._apply_filters()
        self._populate_clubs_list()
    
    def _apply_filters(self):
        """Applica i filtri ai club"""
        self.filtered_clubs = []
        
        country_filter = self.country_var.get()
        league_filter = self.league_var.get()
        reputation_filter = self.reputation_var.get()
        
        for club in self.clubs_data:
            # Filtro paese
            if country_filter != 'Tutti' and club['paese'] != country_filter:
                continue
            
            # Filtro campionato
            if league_filter != 'Tutti' and club['campionato'] != league_filter:
                continue
            
            # Filtro reputazione
            if reputation_filter != 'Tutte':
                reputation = club['reputazione']
                if reputation_filter == 'Alta (15+)' and reputation < 15:
                    continue
                elif reputation_filter == 'Media (10-14)' and (reputation < 10 or reputation >= 15):
                    continue
                elif reputation_filter == 'Bassa (<10)' and reputation >= 10:
                    continue
            
            self.filtered_clubs.append(club)
    
    def _on_club_selected(self, event):
        """Gestisce la selezione di un club"""
        selection = self.clubs_tree.selection()
        if not selection:
            return
        
        # Ottiene i dati del club selezionato
        item = self.clubs_tree.item(selection[0])
        club_name = item['values'][0]
        
        # Trova il club nei dati
        self.selected_club = None
        for club in self.filtered_clubs:
            if club['nome'] == club_name:
                self.selected_club = club
                break
        
        if self.selected_club:
            self._update_club_details()
    
    def _update_club_details(self):
        """Aggiorna i dettagli del club selezionato"""
        if not self.selected_club or not self.info_frame:
            return
        
        # Pulisce il frame dei dettagli
        for widget in self.info_frame.winfo_children():
            widget.destroy()
        
        club = self.selected_club
        
        # Nome del club (titolo)
        title_label = ttk.Label(self.info_frame, text=club['nome'], 
                              font=FONTS['title'], foreground=COLORS['primary'])
        title_label.pack(pady=(0, 10))
        
        # Informazioni base
        info_items = [
            (TEXTS['club_selection']['president'], club['presidente']),
            (TEXTS['club_selection']['city'], club['citta']),
            (TEXTS['club_selection']['country'], club['paese']),
            ('Campionato', club['campionato']),
            (TEXTS['club_selection']['stadium'], club['stadio']),
            (TEXTS['club_selection']['capacity'], f"{club['capienza_stadio']:,}"),
            (TEXTS['club_selection']['reputation'], f"{club['reputazione']}/20"),
            ('Formazione', club['formazione_predefinita'])
        ]
        
        for label, value in info_items:
            self._create_info_row(self.info_frame, label, value)
        
        # Sezione Budget
        ttk.Separator(self.info_frame, orient='horizontal').pack(fill='x', pady=10)
        
        budget_label = ttk.Label(self.info_frame, text="Budget", font=FONTS['subtitle'])
        budget_label.pack(pady=(0, 5))
        
        budget_items = [
            ('Trasferimenti', f"€{club['budget_trasferimenti_eur']:,}"),
            ('Stipendi', f"€{club['budget_stipendi_eur']:,}")
        ]
        
        for label, value in budget_items:
            self._create_info_row(self.info_frame, label, value)
        
        # Sezione Strutture
        ttk.Separator(self.info_frame, orient='horizontal').pack(fill='x', pady=10)
        
        facilities_label = ttk.Label(self.info_frame, text="Strutture", font=FONTS['subtitle'])
        facilities_label.pack(pady=(0, 5))
        
        facilities_items = [
            ('Stadio', f"{club['condizione_stadio']}/10"),
            ('Allenamento', f"{club['strutture_allenamento']}/10"),
            ('Settore Giovanile', f"{club['strutture_giovanili']}/10"),
            ('Reclutamento', f"{club['reclutamento_giovanile']}/10")
        ]
        
        for label, value in facilities_items:
            self._create_info_row(self.info_frame, label, value)
    
    def _create_info_row(self, parent: tk.Widget, label: str, value: str):
        """Crea una riga di informazioni"""
        row_frame = ttk.Frame(parent)
        row_frame.pack(fill='x', pady=2)
        
        label_widget = ttk.Label(row_frame, text=f"{label}:", 
                               font=FONTS['normal'], foreground=COLORS['text_secondary'])
        label_widget.pack(side='left')
        
        value_widget = ttk.Label(row_frame, text=value, 
                               font=FONTS['normal'], foreground=COLORS['text_primary'])
        value_widget.pack(side='right')
    
    def _create_control_buttons(self, parent: tk.Widget):
        """Crea i pulsanti di controllo"""
        buttons_config = [
            {
                'text': TEXTS['club_selection']['back'],
                'command': self._on_back,
                'style': 'Secondary.TButton',
                'side': 'left'
            },
            {
                'text': TEXTS['club_selection']['confirm'],
                'command': self._on_confirm,
                'style': 'Primary.TButton',
                'side': 'right'
            }
        ]
        
        self.create_button_frame(parent, buttons_config)
    
    def _on_back(self):
        """Gestisce il click su Indietro"""
        self.trigger_callback('back_to_menu')
    
    def _on_confirm(self):
        """Gestisce il click su Conferma"""
        if not self.selected_club:
            self.show_message("Seleziona un club prima di continuare", "Attenzione", "warning")
            return
        
        # Conferma la selezione
        confirm = self.show_message(
            f"Sei sicuro di voler gestire il {self.selected_club['nome']}?",
            "Conferma Selezione",
            "question"
        )
        
        if confirm:
            self.trigger_callback('club_selected', self.selected_club)
    
    def setup_events(self):
        """Configura gli eventi"""
        if not self.window:
            return
        
        # Tasti scorciatoia
        self.window.bind('<Return>', lambda e: self._on_confirm())
        self.window.bind('<Escape>', lambda e: self._on_back())
    
    def create_window(self, width: int = 1000, height: int = 700, resizable: bool = True):
        """Crea la finestra di selezione club"""
        return super().create_window(width, height, resizable)
