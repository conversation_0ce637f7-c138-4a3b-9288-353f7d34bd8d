"""
Menu Statistiche
Visualizza statistiche dettagliate di squadra e giocatori
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List
from .base_menu import BaseMenu
from ..gui_config import COLORS, FONTS, ICONS


class StatisticsMenu(BaseMenu):
    """Menu per le statistiche"""
    
    def setup_ui(self):
        """Configura l'interfaccia del menu statistiche"""
        # Header
        self.create_section_header(self.main_frame, "Statistiche", 
                                 "Analisi prestazioni squadra e giocatori")
        
        # Crea le schede per diverse sezioni statistiche
        tabs_config = [
            {'text': 'Squadra', 'setup_func': self._setup_team_stats_tab},
            {'text': 'Giocatori', 'setup_func': self._setup_players_stats_tab},
            {'text': 'Partite', 'setup_func': self._setup_matches_stats_tab},
            {'text': 'Confronti', 'setup_func': self._setup_comparisons_tab}
        ]
        
        self.notebook = self.create_tabs(self.main_frame, tabs_config)
    
    def _setup_team_stats_tab(self, parent: tk.Widget):
        """Configura la scheda statistiche squadra"""
        # Statistiche generali
        general_frame = ttk.LabelFrame(parent, text="Statistiche Generali", padding=15)
        general_frame.pack(fill='x', pady=(0, 10))
        
        # Grid per organizzare le statistiche
        stats_grid = ttk.Frame(general_frame)
        stats_grid.pack(fill='x')
        
        # Colonna sinistra - Attacco
        left_col = ttk.Frame(stats_grid)
        left_col.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_col, text="⚽ Fase Offensiva", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        offensive_stats = [
            ("Gol Segnati", "28", COLORS['success']),
            ("Tiri Totali", "156", COLORS['info']),
            ("Tiri in Porta", "68", COLORS['info']),
            ("Precisione Tiri", "43.6%", COLORS['success']),
            ("Possesso Palla", "58.2%", COLORS['success']),
            ("Passaggi Riusciti", "84.1%", COLORS['success'])
        ]
        
        for label, value, color in offensive_stats:
            self.create_info_row(left_col, label, value, color)
        
        # Colonna destra - Difesa
        right_col = ttk.Frame(stats_grid)
        right_col.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_col, text="🛡️ Fase Difensiva", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        defensive_stats = [
            ("Gol Subiti", "15", COLORS['success']),
            ("Clean Sheets", "8", COLORS['success']),
            ("Contrasti Vinti", "67.3%", COLORS['info']),
            ("Duelli Aerei", "71.2%", COLORS['success']),
            ("Intercetti", "142", COLORS['info']),
            ("Parate Portiere", "89.4%", COLORS['success'])
        ]
        
        for label, value, color in defensive_stats:
            self.create_info_row(right_col, label, value, color)
        
        # Statistiche per competizione
        competitions_frame = ttk.LabelFrame(parent, text="Statistiche per Competizione", padding=15)
        competitions_frame.pack(fill='x', pady=10)
        
        comp_columns = [
            {'id': 'competition', 'text': 'Competizione', 'width': 120},
            {'id': 'matches', 'text': 'Partite', 'width': 60},
            {'id': 'wins', 'text': 'V', 'width': 30},
            {'id': 'draws', 'text': 'P', 'width': 30},
            {'id': 'losses', 'text': 'S', 'width': 30},
            {'id': 'goals_for', 'text': 'GF', 'width': 40},
            {'id': 'goals_against', 'text': 'GS', 'width': 40},
            {'id': 'points', 'text': 'Punti', 'width': 50},
            {'id': 'avg_points', 'text': 'Media', 'width': 50}
        ]
        
        comp_data = [
            ["Serie A", "20", "12", "5", "3", "24", "12", "41", "2.05"],
            ["Coppa Italia", "3", "2", "1", "0", "4", "2", "-", "2.33"],
            ["Champions League", "6", "3", "2", "1", "8", "5", "11", "1.83"],
            ["Totale", "29", "17", "8", "4", "36", "19", "59", "2.03"]
        ]
        
        self.create_data_table(competitions_frame, comp_columns, comp_data, height=5)
        
        # Trend prestazioni
        trends_frame = ttk.LabelFrame(parent, text="Trend Prestazioni", padding=15)
        trends_frame.pack(fill='both', expand=True)
        
        # Barre di progresso per vari indicatori
        trends = [
            ("Forma Attuale (5 partite)", 80, 100),
            ("Prestazioni in Casa", 85, 100),
            ("Prestazioni in Trasferta", 65, 100),
            ("Efficacia Offensiva", 72, 100),
            ("Solidità Difensiva", 88, 100),
            ("Condizione Fisica", 75, 100)
        ]
        
        for label, value, max_val in trends:
            self.create_progress_bar(trends_frame, label, value, max_val)
    
    def _setup_players_stats_tab(self, parent: tk.Widget):
        """Configura la scheda statistiche giocatori"""
        # Filtri
        filters_frame = ttk.Frame(parent)
        filters_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(filters_frame, text="Posizione:").pack(side='left', padx=(0, 5))
        position_combo = ttk.Combobox(filters_frame, values=['Tutte', 'POR', 'DIF', 'CC', 'ATT'], 
                                    state='readonly', width=8)
        position_combo.set('Tutte')
        position_combo.pack(side='left', padx=(0, 20))
        
        ttk.Label(filters_frame, text="Ordinamento:").pack(side='left', padx=(0, 5))
        sort_combo = ttk.Combobox(filters_frame, values=['Gol', 'Assist', 'Presenze', 'Voto Medio'], 
                                state='readonly', width=12)
        sort_combo.set('Gol')
        sort_combo.pack(side='left')
        
        # Tabella statistiche giocatori
        players_frame = ttk.LabelFrame(parent, text="Statistiche Giocatori", padding=10)
        players_frame.pack(fill='both', expand=True)
        
        players_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'position', 'text': 'Pos', 'width': 40},
            {'id': 'appearances', 'text': 'Pres', 'width': 40},
            {'id': 'minutes', 'text': 'Min', 'width': 50},
            {'id': 'goals', 'text': 'Gol', 'width': 40},
            {'id': 'assists', 'text': 'Ass', 'width': 40},
            {'id': 'yellow_cards', 'text': 'GC', 'width': 30},
            {'id': 'red_cards', 'text': 'RC', 'width': 30},
            {'id': 'rating', 'text': 'Voto', 'width': 40}
        ]
        
        players_data = [
            ["Marco Rossi", "ATT", "18", "1420", "12", "5", "2", "0", "7.8"],
            ["Luca Bianchi", "CC", "20", "1680", "3", "8", "4", "0", "7.2"],
            ["Giuseppe Verdi", "DIF", "19", "1710", "1", "2", "3", "1", "7.0"],
            ["Antonio Neri", "POR", "15", "1350", "0", "0", "1", "0", "7.5"],
            ["Francesco Blu", "ATT", "16", "980", "8", "3", "1", "0", "7.4"],
            ["Carlos Silva", "CC", "17", "1200", "4", "6", "2", "0", "7.6"],
            ["Jean Dupont", "DIF", "14", "1260", "2", "1", "5", "0", "6.8"],
            ["Hans Mueller", "CC", "12", "720", "1", "4", "1", "0", "6.9"],
            ["Pablo Garcia", "ATT", "10", "450", "3", "1", "0", "0", "6.7"],
            ["Andrea Gialli", "DIF", "18", "1620", "0", "3", "6", "1", "6.9"]
        ]
        
        players_tree = self.create_data_table(players_frame, players_columns, players_data, height=12)
        
        # Pulsanti azioni
        players_buttons = [
            {'text': '📊 Dettagli Giocatore', 'command': self._player_details, 'style': 'Primary.TButton'},
            {'text': '📈 Grafico Prestazioni', 'command': self._performance_chart, 'style': 'Secondary.TButton'},
            {'text': '📋 Esporta Statistiche', 'command': self._export_stats, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(players_frame, players_buttons)
    
    def _setup_matches_stats_tab(self, parent: tk.Widget):
        """Configura la scheda statistiche partite"""
        # Ultime partite
        recent_frame = ttk.LabelFrame(parent, text="Ultime 10 Partite", padding=15)
        recent_frame.pack(fill='x', pady=(0, 10))
        
        matches_columns = [
            {'id': 'date', 'text': 'Data', 'width': 80},
            {'id': 'opponent', 'text': 'Avversario', 'width': 120},
            {'id': 'venue', 'text': 'Sede', 'width': 60},
            {'id': 'result', 'text': 'Risultato', 'width': 80},
            {'id': 'goals_for', 'text': 'GF', 'width': 30},
            {'id': 'goals_against', 'text': 'GS', 'width': 30},
            {'id': 'possession', 'text': 'Poss%', 'width': 50},
            {'id': 'shots', 'text': 'Tiri', 'width': 40},
            {'id': 'rating', 'text': 'Voto', 'width': 40}
        ]
        
        matches_data = [
            ["15/01", "vs Juventus", "Casa", "V", "2", "1", "52%", "14", "7.8"],
            ["08/01", "@ Milan", "Trasf", "P", "1", "1", "48%", "9", "6.9"],
            ["22/12", "vs Roma", "Casa", "V", "3", "0", "61%", "18", "8.2"],
            ["15/12", "@ Inter", "Trasf", "S", "0", "2", "43%", "6", "6.1"],
            ["08/12", "vs Lazio", "Casa", "V", "2", "1", "58%", "12", "7.5"],
            ["01/12", "@ Atalanta", "Trasf", "V", "1", "0", "45%", "8", "7.3"],
            ["24/11", "vs Fiorentina", "Casa", "P", "2", "2", "55%", "15", "7.0"],
            ["17/11", "@ Torino", "Trasf", "V", "3", "1", "49%", "11", "7.7"],
            ["10/11", "vs Bologna", "Casa", "V", "1", "0", "63%", "16", "7.4"],
            ["03/11", "@ Genoa", "Trasf", "S", "1", "2", "41%", "7", "6.3"]
        ]
        
        self.create_data_table(recent_frame, matches_columns, matches_data, height=10)
        
        # Statistiche dettagliate partite
        detailed_frame = ttk.LabelFrame(parent, text="Statistiche Dettagliate", padding=15)
        detailed_frame.pack(fill='both', expand=True)
        
        # Grid per statistiche dettagliate
        detailed_grid = ttk.Frame(detailed_frame)
        detailed_grid.pack(fill='x')
        
        # Prima colonna
        col1 = ttk.Frame(detailed_grid)
        col1.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        col1_stats = [
            ("Partite Giocate", "20"),
            ("Vittorie", "12"),
            ("Pareggi", "5"),
            ("Sconfitte", "3"),
            ("Gol Fatti", "28"),
            ("Gol Subiti", "15")
        ]
        
        for label, value in col1_stats:
            self.create_info_row(col1, label, value)
        
        # Seconda colonna
        col2 = ttk.Frame(detailed_grid)
        col2.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        col2_stats = [
            ("Punti Totali", "41"),
            ("Media Punti", "2.05"),
            ("Vittorie Consecutive", "3"),
            ("Imbattibilità", "5 partite"),
            ("Miglior Vittoria", "4-0 vs Empoli"),
            ("Peggior Sconfitta", "0-2 @ Inter")
        ]
        
        for label, value in col2_stats:
            self.create_info_row(col2, label, value)
        
        # Terza colonna
        col3 = ttk.Frame(detailed_grid)
        col3.pack(side='right', fill='both', expand=True)
        
        col3_stats = [
            ("Casa (V-P-S)", "8-2-0"),
            ("Trasferta (V-P-S)", "4-3-3"),
            ("Primo Tempo", "15 gol"),
            ("Secondo Tempo", "13 gol"),
            ("Clean Sheets", "8"),
            ("Partite con 2+ Gol", "12")
        ]
        
        for label, value in col3_stats:
            self.create_info_row(col3, label, value)
    
    def _setup_comparisons_tab(self, parent: tk.Widget):
        """Configura la scheda confronti"""
        # Confronto con altre squadre
        comparison_frame = ttk.LabelFrame(parent, text="Confronto con Altre Squadre", padding=15)
        comparison_frame.pack(fill='x', pady=(0, 10))
        
        comparison_columns = [
            {'id': 'team', 'text': 'Squadra', 'width': 100},
            {'id': 'position', 'text': 'Pos', 'width': 40},
            {'id': 'points', 'text': 'Punti', 'width': 50},
            {'id': 'goals_for', 'text': 'GF', 'width': 40},
            {'id': 'goals_against', 'text': 'GS', 'width': 40},
            {'id': 'goal_diff', 'text': 'Diff', 'width': 40},
            {'id': 'possession', 'text': 'Poss%', 'width': 50},
            {'id': 'shots_pg', 'text': 'Tiri/P', 'width': 50}
        ]
        
        comparison_data = [
            ["Juventus", "1°", "48", "32", "8", "+24", "59.2%", "16.8"],
            ["Milan", "2°", "45", "29", "12", "+17", "57.1%", "15.2"],
            ["Napoli (Noi)", "3°", "41", "28", "15", "+13", "58.2%", "14.6"],
            ["Inter", "4°", "39", "26", "14", "+12", "56.8%", "13.9"],
            ["Roma", "5°", "36", "24", "18", "+6", "54.3%", "12.7"],
            ["Lazio", "6°", "34", "22", "19", "+3", "53.1%", "11.8"]
        ]
        
        self.create_data_table(comparison_frame, comparison_columns, comparison_data, height=6)
        
        # Confronto storico
        historical_frame = ttk.LabelFrame(parent, text="Confronto Storico (Ultime 5 Stagioni)", padding=15)
        historical_frame.pack(fill='x', pady=10)
        
        historical_columns = [
            {'id': 'season', 'text': 'Stagione', 'width': 80},
            {'id': 'position', 'text': 'Posizione', 'width': 80},
            {'id': 'points', 'text': 'Punti', 'width': 60},
            {'id': 'goals_for', 'text': 'Gol Fatti', 'width': 80},
            {'id': 'goals_against', 'text': 'Gol Subiti', 'width': 80},
            {'id': 'top_scorer', 'text': 'Capocannoniere', 'width': 120},
            {'id': 'goals', 'text': 'Gol', 'width': 40}
        ]
        
        historical_data = [
            ["2024/25", "3° (in corso)", "41", "28", "15", "M. Rossi", "12"],
            ["2023/24", "7°", "52", "48", "42", "L. Verdi", "18"],
            ["2022/23", "4°", "65", "58", "38", "A. Bianchi", "22"],
            ["2021/22", "9°", "47", "41", "51", "G. Neri", "15"],
            ["2020/21", "5°", "59", "52", "45", "M. Rossi", "19"]
        ]
        
        self.create_data_table(historical_frame, historical_columns, historical_data, height=5)
        
        # Analisi prestazioni
        analysis_frame = ttk.LabelFrame(parent, text="Analisi Prestazioni", padding=15)
        analysis_frame.pack(fill='both', expand=True)
        
        # Punti di forza e debolezza
        analysis_grid = ttk.Frame(analysis_frame)
        analysis_grid.pack(fill='x')
        
        strengths_col = ttk.Frame(analysis_grid)
        strengths_col.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(strengths_col, text="💪 Punti di Forza", font=FONTS['subtitle'], 
                 foreground=COLORS['success']).pack(anchor='w', pady=(0, 10))
        
        strengths = [
            "Solidità difensiva (0.75 gol subiti/partita)",
            "Efficacia sui calci piazzati (8 gol)",
            "Prestazioni in casa (8V-2P-0S)",
            "Possesso palla (58.2% media)",
            "Forma attuale (3 vittorie consecutive)"
        ]
        
        for strength in strengths:
            strength_frame = ttk.Frame(strengths_col)
            strength_frame.pack(fill='x', pady=2)
            ttk.Label(strength_frame, text="✅", font=FONTS['normal']).pack(side='left', padx=(0, 5))
            ttk.Label(strength_frame, text=strength, font=FONTS['small'], 
                     wraplength=200).pack(side='left')
        
        weaknesses_col = ttk.Frame(analysis_grid)
        weaknesses_col.pack(side='right', fill='both', expand=True)
        
        ttk.Label(weaknesses_col, text="⚠️ Aree di Miglioramento", font=FONTS['subtitle'], 
                 foreground=COLORS['warning']).pack(anchor='w', pady=(0, 10))
        
        weaknesses = [
            "Prestazioni in trasferta (4V-3P-3S)",
            "Finalizzazione (14.6 tiri/partita, 1.4 gol)",
            "Disciplina (42 cartellini gialli)",
            "Rotazioni limitate (11 titolari fissi)",
            "Dipendenza da M. Rossi (43% dei gol)"
        ]
        
        for weakness in weaknesses:
            weakness_frame = ttk.Frame(weaknesses_col)
            weakness_frame.pack(fill='x', pady=2)
            ttk.Label(weakness_frame, text="⚠️", font=FONTS['normal']).pack(side='left', padx=(0, 5))
            ttk.Label(weakness_frame, text=weakness, font=FONTS['small'], 
                     wraplength=200).pack(side='left')
    
    # Metodi placeholder per le azioni
    def _player_details(self):
        self.show_message("Dettagli Giocatore", "Funzionalità in sviluppo", "info")
    
    def _performance_chart(self):
        self.show_message("Grafico Prestazioni", "Funzionalità in sviluppo", "info")
    
    def _export_stats(self):
        self.show_message("Esporta Statistiche", "Funzionalità in sviluppo", "info")
