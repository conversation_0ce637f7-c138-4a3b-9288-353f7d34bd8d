#!/usr/bin/env python3
"""
Test script per l'interfaccia grafica del Football Manager
"""

import sys
import os

# Aggiungi il percorso src al PYTHONPATH
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from interface.main_gui import FootballManagerGUI
    
    def main():
        """Funzione principale per testare l'interfaccia"""
        print("🚀 Avvio test interfaccia grafica Football Manager...")
        print("📋 Controllo dipendenze...")
        
        # Test import delle dipendenze principali
        try:
            import tkinter as tk
            from tkinter import ttk
            print("✅ Tkinter importato correttamente")
        except ImportError as e:
            print(f"❌ Errore import Tkinter: {e}")
            return False
        
        try:
            from data.data_loader import DataLoader
            print("✅ DataLoader importato correttamente")
        except ImportError as e:
            print(f"❌ Errore import DataLoader: {e}")
            return False
        
        try:
            from game.game_engine import GameEngine
            print("✅ GameEngine importato correttamente")
        except ImportError as e:
            print(f"❌ Errore import GameEngine: {e}")
            return False
        
        # Test import dei menu
        menu_modules = [
            'interface.menus.team_menu',
            'interface.menus.finances_menu',
            'interface.menus.calendar_menu',
            'interface.menus.tactics_menu',
            'interface.menus.transfers_menu',
            'interface.menus.statistics_menu',
            'interface.menus.facilities_menu',
            'interface.menus.staff_menu',
            'interface.menus.youth_menu',
            'interface.menus.media_menu',
            'interface.menus.board_menu'
        ]
        
        for module_name in menu_modules:
            try:
                __import__(module_name)
                print(f"✅ {module_name} importato correttamente")
            except ImportError as e:
                print(f"❌ Errore import {module_name}: {e}")
                return False
        
        print("\n🎮 Avvio interfaccia grafica...")
        
        # Crea e avvia l'interfaccia
        try:
            app = FootballManagerGUI()
            print("✅ Interfaccia creata correttamente")
            
            # Avvia l'applicazione
            print("🖥️  Apertura finestra principale...")
            app.run()
            
        except Exception as e:
            print(f"❌ Errore durante l'avvio dell'interfaccia: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
    
    if __name__ == "__main__":
        print("=" * 60)
        print("🏆 FOOTBALL MANAGER - TEST INTERFACCIA GRAFICA")
        print("=" * 60)
        
        success = main()
        
        if success:
            print("\n✅ Test completato con successo!")
        else:
            print("\n❌ Test fallito. Controlla gli errori sopra.")
            sys.exit(1)

except ImportError as e:
    print(f"❌ Errore critico nell'import del modulo principale: {e}")
    print("🔧 Assicurati che tutti i file siano presenti nella cartella src/")
    sys.exit(1)
