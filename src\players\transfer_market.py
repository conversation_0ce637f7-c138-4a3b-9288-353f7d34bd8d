"""
Football Manager - Transfer Market
Manages player transfers between clubs and contract negotiations.
"""

from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple
from datetime import datetime, date
import random

from players.player import Player
from club.club import Club

@dataclass
class TransferBid:
    buying_club: Club
    selling_club: Club
    player: Player
    bid_amount: int
    offer_date: date
    response_deadline: date
    status: str = "pending"  # pending, accepted, rejected, counter_offer
    
@dataclass
class TransferDeal:
    player: Player
    from_club: Club
    to_club: Club
    transfer_fee: int
    contract_length: int  # years
    wage: int  # weekly wage
    transfer_date: date
    bonus_clauses: Dict[str, int] = None

class TransferMarket:
    def __init__(self):
        self.available_players: List[Player] = []
        self.transfer_bids: List[TransferBid] = []
        self.completed_transfers: List[TransferDeal] = []
        self.transfer_window_open = False
        self.current_season = "2025/2026"
        
    def list_player_for_sale(self, player: Player, asking_price: int, 
                           current_club: Club) -> bool:
        """List a player on the transfer market"""
        if player not in current_club.players:
            return False
            
        # Remove player from current club
        current_club.players.remove(player)
        
        # Add to available players with asking price
        player.market_value = asking_price
        self.available_players.append(player)
        
        return True
        
    def remove_player_from_market(self, player: Player) -> bool:
        """Remove a player from the transfer market"""
        if player in self.available_players:
            self.available_players.remove(player)
            return True
        return False
        
    def submit_bid(self, buying_club: Club, player: Player, 
                  offer_amount: int) -> Optional[TransferBid]:
        """Submit a transfer bid for a player"""
        # Check if player is available
        if player not in self.available_players:
            return None
            
        # Find current club (player should have this info)
        selling_club = None
        for club in [bid.selling_club for bid in self.transfer_bids if bid.player == player]:
            selling_club = club
            break
            
        # If we can't find selling club, we can't process bid
        if not selling_club:
            return None
            
        # Check if buying club can afford the transfer
        if not buying_club.can_afford_transfer(offer_amount):
            return None
            
        # Create bid
        bid = TransferBid(
            buying_club=buying_club,
            selling_club=selling_club,
            player=player,
            bid_amount=offer_amount,
            offer_date=date.today(),
            response_deadline=date.today().replace(day=date.today().day + 3)  # 3 days to respond
        )
        
        self.transfer_bids.append(bid)
        return bid
        
    def process_bids(self) -> List[TransferDeal]:
        """Process all pending bids and generate transfers"""
        completed_deals = []
        
        for bid in self.transfer_bids:
            if bid.status == "pending":
                # Process the bid
                deal = self._process_bid(bid)
                if deal:
                    completed_deals.append(deal)
                    self.completed_transfers.append(deal)
                    
        # Remove processed bids
        self.transfer_bids = [bid for bid in self.transfer_bids if bid.status == "pending"]
        
        return completed_deals
        
    def _process_bid(self, bid: TransferBid) -> Optional[TransferDeal]:
        """Process a single bid and return a transfer deal if accepted"""
        # Simple AI decision making for selling club
        # In reality, this would be more complex based on club needs, finances, etc.
        
        # Accept if bid is above asking price or within 10% of it
        if bid.bid_amount >= bid.player.market_value * 0.9:
            # Accept bid
            bid.status = "accepted"
            
            # Create transfer deal
            deal = TransferDeal(
                player=bid.player,
                from_club=bid.selling_club,
                to_club=bid.buying_club,
                transfer_fee=bid.bid_amount,
                contract_length=random.randint(3, 5),  # 3-5 year contract typically
                wage=self._calculate_wage(bid.player, bid.buying_club),
                transfer_date=date.today(),
                bonus_clauses=self._generate_bonus_clauses(bid.player)
            )
            
            # Process the transfer
            self._execute_transfer(deal)
            
            return deal
        else:
            # Reject bid
            bid.status = "rejected"
            return None
            
    def _calculate_wage(self, player: Player, club: Club) -> int:
        """Calculate appropriate wage for a player at a club"""
        # Base wage based on player rating and club budget
        base_wage = player.calculate_overall_rating() * 1000
        
        # Adjust based on club budget (higher budget clubs pay more)
        budget_factor = min(2.0, max(0.5, club.finances.wage_budget / 10000000))
        
        # Adjust based on player age (peak years get higher wages)
        if 24 <= player.age <= 30:
            age_factor = 1.2
        elif player.age > 30:
            age_factor = 0.8
        else:
            age_factor = 1.0
            
        wage = int(base_wage * budget_factor * age_factor)
        
        # Ensure wage is within club budget constraints
        max_wage = club.finances.wage_budget // 52 // 25  # Roughly 1/25 of weekly budget for one player
        return min(wage, max_wage)
        
    def _generate_bonus_clauses(self, player: Player) -> Dict[str, int]:
        """Generate performance bonus clauses for player contract"""
        clauses = {}
        
        # Appearance bonus (for established players)
        if player.calculate_overall_rating() > 70:
            clauses["appearance_bonus"] = 5000
            
        # Goal/assist bonus (for attacking players)
        if player.position in [player.position.ST, player.position.W, player.position.AM]:
            clauses["goal_bonus"] = 10000
            clauses["assist_bonus"] = 5000
            
        # Clean sheet bonus (for defensive players)
        if player.position in [player.position.CB, player.position.GK, player.position.LB, player.position.RB]:
            clauses["clean_sheet_bonus"] = 3000
            
        # Win bonus
        clauses["win_bonus"] = 2000
        
        return clauses
        
    def _execute_transfer(self, deal: TransferDeal):
        """Execute a transfer deal between clubs"""
        # Remove player from selling club
        if deal.player in deal.from_club.players:
            deal.from_club.players.remove(deal.player)
            
        # Add player to buying club
        deal.to_club.players.append(deal.player)
        deal.player.current_club = deal.to_club.name
        
        # Update finances
        deal.from_club.update_finances(revenue=deal.transfer_fee)
        deal.to_club.update_finances(expense=deal.transfer_fee)
        
        # Remove player from market
        if deal.player in self.available_players:
            self.available_players.remove(deal.player)
            
    def get_available_players(self, position: str = None, max_value: int = None) -> List[Player]:
        """Get list of available players, optionally filtered by position or value"""
        players = self.available_players
        
        if position:
            players = [p for p in players if p.position.value.lower() == position.lower()]
            
        if max_value:
            players = [p for p in players if p.market_value <= max_value]
            
        return players
        
    def get_club_transfers(self, club: Club) -> Dict[str, List[TransferDeal]]:
        """Get all transfers involving a specific club"""
        transfers = {
            "bought": [deal for deal in self.completed_transfers if deal.to_club == club],
            "sold": [deal for deal in self.completed_transfers if deal.from_club == club]
        }
        return transfers
        
    def open_transfer_window(self):
        """Open the transfer window"""
        self.transfer_window_open = True
        
    def close_transfer_window(self):
        """Close the transfer window"""
        self.transfer_window_open = False
        
    def get_market_summary(self) -> Dict[str, int]:
        """Get summary statistics of the transfer market"""
        total_value = sum(player.market_value for player in self.available_players)
        return {
            "available_players": len(self.available_players),
            "total_market_value": total_value,
            "pending_bids": len([bid for bid in self.transfer_bids if bid.status == "pending"]),
            "completed_transfers": len(self.completed_transfers)
        }
        
    def generate_free_agents(self, count: int = 20) -> List[Player]:
        """Generate free agent players for the market"""
        # This would typically be implemented with a player generator
        # For now, we'll just return an empty list
        return []