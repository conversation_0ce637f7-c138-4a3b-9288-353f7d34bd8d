"""
Demo script showing a complete season simulation with all modules working together
"""

import sys
from pathlib import Path
from datetime import date, timedelta

# Add src directory to path for imports
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Add src to Python path for relative imports
sys.path.insert(0, str(Path(__file__).parent))

from game.game_engine import GameEngine
from game.season_manager import SeasonManager
from data.data_loader import DataLoader
from data.name_generator import NameGenerator
from staff.staff_generator import StaffGenerator
from club.club import Club

def demo_complete_season():
    """Demonstrate a complete season simulation"""
    print("=== Football Manager - Complete Season Demo ===")
    print()
    
    # Initialize core systems
    print("Initializing systems...")
    data_loader = DataLoader()
    data_loader.load_all_data()
    name_gen = NameGenerator(data_loader)
    staff_gen = StaffGenerator(data_loader, name_gen)
    game_engine = GameEngine()
    
    # Create season manager
    season_manager = SeasonManager(game_engine)
    
    # Load Italian Serie A data for demo
    print("Loading league data...")
    serie_a_data = data_loader.get_league_data("Italia", "Serie_A")
    
    # Use club data directly without creating Club objects first
    clubs_data = serie_a_data["squadre"][:8]  # Use first 8 clubs for demo
    
    # Prepare league data with club data
    league_data = {
        "nome_campionato": serie_a_data["nome_campionato"],
        "paese": serie_a_data["paese"],
        "livello_competizione": serie_a_data["livello_competizione"],
        "stagione": "2025/2026",
        "clubs": clubs_data  # Pass raw club data
    }
    
    # Initialize season
    print("Initializing season...")
    season_manager.initialize_season([league_data])
    
    print(f"Season initialized with {len(season_manager.leagues[0].clubs)} clubs")
    print(f"Total fixtures: {len(season_manager.leagues[0].fixtures)}")
    print()
    
    # Show initial league table
    print("Initial League Table:")
    table = season_manager.get_league_table("Serie A")
    for i, entry in enumerate(table[:5]):  # Show top 5
        print(f"  {entry.position}. {entry.club.name} - {entry.points} pts")
    print()
    
    # Simulate first few matchdays
    print("Simulating matchdays...")
    for matchday in range(1, 4):  # Simulate first 3 matchdays
        print(f"\n--- Matchday {matchday} ---")
        
        # Advance to next matchday
        success = season_manager.advance_to_next_matchday()
        if not success:
            print("Season finished!")
            break
            
        # Show updated table
        table = season_manager.get_league_table("Serie A")
        print("Updated League Table (Top 5):")
        for i, entry in enumerate(table[:5]):
            print(f"  {entry.position}. {entry.club.name} - {entry.points} pts ({entry.played} played)")
            
        # Process weekly activities
        current_date = date(2025, 8, 15) + timedelta(weeks=matchday)
        season_manager.process_weekly_activities(current_date)
        
    print()
    
    # Show top scorers
    print("Top Scorers:")
    top_scorers = season_manager.get_top_scorers("Serie A", 5)
    for i, scorer in enumerate(top_scorers, 1):
        print(f"  {i}. {scorer['player'].full_name} ({scorer['club'].name}) - {scorer['goals']} goals")
        
    print()
    
    # Show season status
    status = season_manager.get_season_status()
    print("Season Status:")
    for key, value in status.items():
        print(f"  {key}: {value}")
        
    print()
    
    # Show financial status of a club
    if season_manager.budget_managers:
        club_name = list(season_manager.budget_managers.keys())[0]
        budget_manager = season_manager.budget_managers[club_name]
        projection = budget_manager.get_annual_projection()
        print(f"Financial Projection for {club_name}:")
        for key, value in projection.items():
            print(f"  {key}: €{value:,}")
            
    print()
    print("=== Demo Complete ===")

def main():
    """Run the demo"""
    try:
        demo_complete_season()
    except Exception as e:
        print(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()