"""
Football Manager - Main Entry Point
Launch point for the football management game.
"""

import sys
import os
from pathlib import Path

# Add src directory to path for imports
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from game.game_engine import GameEngine, GameSpeed
from data.data_loader import DataLoader
from data.name_generator import NameGenerator
from club.club import Club
from staff.coaching_staff import Coach
from staff.staff_generator import StaffGenerator
from ai.coach_ai import <PERSON><PERSON><PERSON>
from competitions.league_manager import League
from competitions.match_engine import MatchEngine

def main():
    """Main game entry point"""
    print("=== Football Manager - Presidential Edition ===")
    print("Loading game data...")
    
    # Initialize data loader
    data_loader = DataLoader()
    if not data_loader.load_all_data():
        print("Error: Could not load game data!")
        return
    
    # Validate data integrity
    data_status = data_loader.validate_data_integrity()
    print(f"Data Status: {data_status}")
    
    # Initialize name generator
    name_gen = NameGenerator(data_loader)
    
    # Initialize staff generator
    staff_gen = StaffGenerator(data_loader, name_gen)
    
    # Initialize game engine
    game = GameEngine()
    
    print("\nAvailable clubs:")
    clubs_data = data_loader.get_clubs_by_country("Italia")
    
    for i, club_data in enumerate(clubs_data[:10]):  # Show first 10 clubs
        print(f"{i+1}. {club_data['nome']} ({club_data['citta']})")
    
    print("\nDemo: Creating club objects...")
    
    # Create some example clubs
    for club_data in clubs_data[:3]:
        club = Club(club_data)
        print(f"\n{club}")
        print(f"  Stadium: {club.facilities.stadium_name} ({club.facilities.stadium_capacity:,})")
        print(f"  Budget: €{club.finances.transfer_budget:,}")
        print(f"  Reputation: {club.reputation.overall}/20")
        
        # Generate staff for the club
        staff_team = staff_gen.generate_staff_team(club.reputation.overall)
        print(f"  Staff: {len(staff_team)} members")
        for staff in staff_team[:3]:  # Show first 3 staff members
            print(f"    - {staff.full_name} ({staff.role.value}) - Rating: {staff.calculate_coaching_rating()}")
    
    print("\nDemo: Generating player names...")
    
    # Generate some example names
    for country in ['Italia', 'Brasile', 'Germania', 'Spagna']:
        first, last = name_gen.generate_player_name(country)
        print(f"{country}: {first} {last}")
    
    print(f"\nName generator statistics: {name_gen.get_name_statistics()}")
    
    print("\nDemo: League Management...")
    
    # Create a small league for demonstration
    league_clubs_data = clubs_data[:6]  # First 6 clubs
    league_clubs = [Club(club_data) for club_data in league_clubs_data]
    
    # Create league data
    league_data = {
        "nome_campionato": "Serie A Demo",
        "paese": "Italia",
        "livello_competizione": 1,
        "stagione": "2025/2026"
    }
    
    # Create league
    league = League(league_data, league_clubs)
    print(f"Created league: {league.name} with {len(league.clubs)} clubs")
    
    # Generate fixtures
    fixtures = league.generate_fixtures()
    print(f"Generated {len(fixtures)} fixtures for {league.total_matchdays} matchdays")
    
    # Show first matchday fixtures
    first_matchday = league.get_current_matchday_fixtures()
    print("\nFirst matchday fixtures:")
    for fixture in first_matchday:
        print(f"  {fixture.home_team.name} vs {fixture.away_team.name}")
    
    print("\nDemo: Match Simulation...")
    
    # Initialize match engine
    match_engine = MatchEngine()
    
    # Simulate a match between first two clubs
    home_club = league_clubs[0]
    away_club = league_clubs[1]
    
    # Generate coaches for the clubs
    home_coach = staff_gen.generate_coach(home_club.country, "high")
    away_coach = staff_gen.generate_coach(away_club.country, "high")
    
    print(f"Match: {home_club.name} (Coach: {home_coach.full_name}) vs {away_club.name} (Coach: {away_coach.full_name})")
    
    # Simulate match
    result = match_engine.simulate_match(home_club, away_club)
    print(f"Result: {match_engine.get_match_result_string(result)}")
    print(f"Match events: {len(result.events)}")
    
    # Record match result in league
    if first_matchday:
        first_fixture = first_matchday[0]
        league.play_match(first_fixture, result.home_goals, result.away_goals)
        
        # Show updated table
        table = league.get_table()
        print("\nUpdated League Table (Top 3):")
        for i, entry in enumerate(table[:3]):
            print(f"  {entry.position}. {entry.club.name} - {entry.points} pts ({entry.played} played)")
    
    print("\n=== Game System Ready ===")
    print("Core systems initialized successfully!")
    
if __name__ == "__main__":
    main()