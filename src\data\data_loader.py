"""
Football Manager - Data Loader
Loads and processes all JSON data files for the game.
"""

import json
import os
from typing import Dict, List, Optional
from pathlib import Path

class DataLoader:
    def __init__(self, data_path: str = "data"):
        self.data_path = Path(data_path)
        self.player_names = {}
        self.fifa_codes = {}
        self.confederations = {}
        self.european_calendar = {}
        self.leagues_data = {}
        
    def load_all_data(self) -> bool:
        """Load all game data from JSON files"""
        try:
            self._load_player_names()
            self._load_fifa_codes()
            self._load_confederations()
            self._load_european_calendar()
            self._load_leagues_data()
            return True
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
            
    def _load_player_names(self):
        """Load player names database"""
        file_path = self.data_path / "player_names.json"
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                self.player_names = json.load(f)
                
    def _load_fifa_codes(self):
        """Load FIFA country codes"""
        file_path = self.data_path / "fifa_country_codes.json"
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                self.fifa_codes = json.load(f)
                
    def _load_confederations(self):
        """Load FIFA confederations"""
        file_path = self.data_path / "fifa_confederations.json"
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                self.confederations = json.load(f)
                
    def _load_european_calendar(self):
        """Load European competitions calendar"""
        file_path = self.data_path / "european_calendar.json"
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                self.european_calendar = json.load(f)
                
    def _load_leagues_data(self):
        """Load all league data from UEFA directory"""
        leagues_path = self.data_path / "leagues" / "UEFA"
        if not leagues_path.exists():
            return
            
        for country_dir in leagues_path.iterdir():
            if country_dir.is_dir():
                country_name = country_dir.name
                self.leagues_data[country_name] = {}
                
                for league_file in country_dir.glob("*.json"):
                    league_name = league_file.stem
                    with open(league_file, 'r', encoding='utf-8') as f:
                        self.leagues_data[country_name][league_name] = json.load(f)
                        
    def get_league_data(self, country: str, league: str) -> Optional[Dict]:
        """Get specific league data"""
        return self.leagues_data.get(country, {}).get(league)
        
    def get_all_clubs(self) -> List[Dict]:
        """Get all clubs from all leagues"""
        clubs = []
        for country_data in self.leagues_data.values():
            for league_data in country_data.values():
                if 'squadre' in league_data:
                    clubs.extend(league_data['squadre'])
        return clubs
        
    def get_clubs_by_country(self, country: str) -> List[Dict]:
        """Get all clubs from a specific country"""
        clubs = []
        if country in self.leagues_data:
            for league_data in self.leagues_data[country].values():
                if 'squadre' in league_data:
                    clubs.extend(league_data['squadre'])
        return clubs
        
    def get_club_by_name(self, club_name: str) -> Optional[Dict]:
        """Find a club by name across all leagues"""
        for country_data in self.leagues_data.values():
            for league_data in country_data.values():
                if 'squadre' in league_data:
                    for club in league_data['squadre']:
                        if club['nome'] == club_name:
                            return club
        return None
        
    def get_names_for_country(self, country: str) -> Dict[str, List]:
        """Get name lists for a specific country"""
        return self.player_names.get(country, {
            'first_names': ['Unknown'],
            'last_names': ['Player']
        })
        
    def get_european_competitions_calendar(self) -> Dict:
        """Get European competitions calendar"""
        return self.european_calendar.get('calendario_europeo', {})
        
    def get_confederation_countries(self, confederation: str) -> List[str]:
        """Get countries in a confederation"""
        return self.confederations.get(confederation, {}).get('countries', [])
        
    def validate_data_integrity(self) -> Dict[str, bool]:
        """Validate loaded data integrity"""
        return {
            'player_names': bool(self.player_names),
            'fifa_codes': bool(self.fifa_codes),
            'confederations': bool(self.confederations),
            'european_calendar': bool(self.european_calendar),
            'leagues_data': bool(self.leagues_data)
        }