"""
Football Manager - Coach AI
AI system for making tactical decisions during matches and training.
"""

from typing import List, Dict, Optional, Tuple
from enum import Enum
import random
from datetime import datetime

from staff.coaching_staff import Coach, PreferredFormation
from players.player import Player, Position

class MatchSituation(Enum):
    GOAL_UP = "goal_up"
    GOAL_DOWN = "goal_down"
    DRAW = "draw"
    CLOSE = "close"
    EASY_WIN = "easy_win"
    HEAVY_LOSS = "heavy_loss"

class TacticalInstruction(Enum):
    ATTACK = "attack"
    DEFEND = "defend"
    BALANCED = "balanced"
    PRESS_HIGH = "press_high"
    SIT_BACK = "sit_back"
    PLAY_WIDTH = "play_width"
    PLAY_THROUGH_MIDDLE = "play_through_middle"

class CoachAI:
    def __init__(self, coach: Coach):
        self.coach = coach
        self.tactical_preference = self._determine_preference()
        self.preferred_formations = self._get_preferred_formations()
        
    def _determine_preference(self) -> str:
        """Determine coach's tactical preference based on attributes"""
        # Higher tactical knowledge leads to more attacking preference
        if self.coach.tactical_knowledge >= 15:
            return "attacking"
        elif self.coach.tactical_knowledge >= 10:
            return "balanced"
        else:
            return "defensive"
            
    def _get_preferred_formations(self) -> List[PreferredFormation]:
        """Get list of formations the coach prefers based on experience"""
        # Start with coach's preferred formation
        formations = [self.coach.preferred_formation]
        
        # Add other formations based on adaptability
        if self.coach.adaptability >= 15:
            formations.extend([
                PreferredFormation.FOUR_THREE_THREE,
                PreferredFormation.THREE_FIVE_TWO,
                PreferredFormation.FOUR_TWO_THREE_ONE
            ])
        elif self.coach.adaptability >= 10:
            formations.extend([
                PreferredFormation.FOUR_FOUR_TWO,
                PreferredFormation.THREE_FOUR_THREE
            ])
            
        # Remove duplicates while preserving order
        seen = set()
        unique_formations = []
        for formation in formations:
            if formation not in seen:
                seen.add(formation)
                unique_formations.append(formation)
                
        return unique_formations
        
    def select_formation(self, available_players: List[Player], 
                        opponent_strength: int = 10) -> Tuple[PreferredFormation, List[Player]]:
        """Select best formation and players for the match"""
        # First, try the coach's preferred formation
        formation = self.preferred_formations[0]
        selected_players = self._select_players_for_formation(formation, available_players)
        
        # If we don't have enough quality players for preferred formation, adapt
        if not selected_players or self._evaluate_squad_quality(selected_players) < 70:
            # Try other formations in order of preference
            for alt_formation in self.preferred_formations[1:]:
                alt_players = self._select_players_for_formation(alt_formation, available_players)
                if alt_players and self._evaluate_squad_quality(alt_players) > 70:
                    formation = alt_formation
                    selected_players = alt_players
                    break
                    
        return formation, selected_players
        
    def _select_players_for_formation(self, formation: PreferredFormation, 
                                    available_players: List[Player]) -> List[Player]:
        """Select players for a specific formation"""
        # This is a simplified player selection algorithm
        selected_players = []
        used_players = set()
        
        # Formation patterns (simplified)
        formation_patterns = {
            PreferredFormation.FOUR_FOUR_TWO: {
                Position.GK: 1, Position.CB: 2, Position.FB: 2,
                Position.CM: 2, Position.WM: 2, Position.ST: 2
            },
            PreferredFormation.FOUR_THREE_THREE: {
                Position.GK: 1, Position.CB: 2, Position.FB: 2,
                Position.CM: 3, Position.W: 2, Position.ST: 1
            },
            PreferredFormation.THREE_FIVE_TWO: {
                Position.GK: 1, Position.CB: 3, Position.WM: 5, Position.ST: 2
            },
            PreferredFormation.FOUR_TWO_THREE_ONE: {
                Position.GK: 1, Position.CB: 2, Position.FB: 2,
                Position.DM: 2, Position.AM: 3, Position.ST: 1
            }
        }
        
        pattern = formation_patterns.get(formation, formation_patterns[PreferredFormation.FOUR_FOUR_TWO])
        
        # Select players for each position
        for position_type, count in pattern.items():
            candidates = [p for p in available_players 
                         if p not in used_players and self._can_play_position(p, position_type)]
            candidates.sort(key=lambda p: p.calculate_overall_rating(), reverse=True)
            
            for player in candidates[:count]:
                selected_players.append(player)
                used_players.add(player)
                
        return selected_players
        
    def _can_play_position(self, player: Player, position_type: Position) -> bool:
        """Check if player can play in a specific position type"""
        # Simplified position compatibility
        if position_type == Position.GK:
            return player.position == Position.GK
        elif position_type in [Position.CB, Position.FB]:
            return player.position in [Position.CB, Position.LB, Position.RB, Position.SW]
        elif position_type == Position.CM:
            return player.position in [Position.CM, Position.DM, Position.AM]
        elif position_type in [Position.WM, Position.W]:
            return player.position in [Position.LM, Position.RM, Position.W, Position.AM]
        elif position_type == Position.ST:
            return player.position in [Position.ST, Position.CF, Position.LF, Position.RF]
        elif position_type == Position.DM:
            return player.position in [Position.DM, Position.CM, Position.CB]
        elif position_type == Position.AM:
            return player.position in [Position.AM, Position.CM, Position.W]
        else:
            return True
            
    def _evaluate_squad_quality(self, players: List[Player]) -> int:
        """Evaluate overall squad quality (0-100)"""
        if not players:
            return 0
            
        total_rating = sum(player.calculate_overall_rating() for player in players)
        return total_rating // len(players)
        
    def adjust_tactics(self, match_minute: int, current_score: Tuple[int, int], 
                      opponent_strength: int) -> List[TacticalInstruction]:
        """Adjust tactics during the match based on situation"""
        instructions = []
        
        # Determine match situation
        home_goals, away_goals = current_score
        goal_difference = home_goals - away_goals
        match_situation = self._determine_match_situation(goal_difference)
        
        # Adjust tactics based on situation and coach preference
        if match_situation == MatchSituation.GOAL_DOWN:
            if self.tactical_preference == "attacking":
                instructions.extend([TacticalInstruction.ATTACK, TacticalInstruction.PRESS_HIGH])
            else:
                # More defensive coaches might still attack but cautiously
                instructions.append(TacticalInstruction.ATTACK)
                
        elif match_situation == MatchSituation.GOAL_UP:
            if self.tactical_preference == "defensive":
                instructions.extend([TacticalInstruction.DEFEND, TacticalInstruction.SIT_BACK])
            else:
                # Attacking coaches might still press to extend lead
                instructions.append(TacticalInstruction.PRESS_HIGH)
                
        elif match_situation == MatchSituation.HEAVY_LOSS:
            # In heavy loss situations, even attacking coaches might defend
            instructions.append(TacticalInstruction.DEFEND)
            
        elif match_situation == MatchSituation.EASY_WIN:
            # In easy win situations, coaches might rest players or maintain position
            if self.coach.adaptability >= 12:
                instructions.append(TacticalInstruction.BALANCED)
                
        # Time-based adjustments
        if match_minute >= 75:
            # Late game adjustments
            if goal_difference > 0:
                instructions.append(TacticalInstruction.SIT_BACK)
            elif goal_difference < 0:
                instructions.append(TacticalInstruction.PRESS_HIGH)
                
        # Remove duplicates
        return list(set(instructions))
        
    def _determine_match_situation(self, goal_difference: int) -> MatchSituation:
        """Determine match situation based on goal difference"""
        if goal_difference >= 3:
            return MatchSituation.EASY_WIN
        elif goal_difference <= -3:
            return MatchSituation.HEAVY_LOSS
        elif goal_difference > 0:
            return MatchSituation.GOAL_UP
        elif goal_difference < 0:
            return MatchSituation.GOAL_DOWN
        else:
            return MatchSituation.DRAW
            
    def make_substitutions(self, current_players: List[Player], 
                          available_substitutes: List[Player], 
                          match_minute: int, current_score: Tuple[int, int]) -> List[Tuple[Player, Player]]:
        """Make substitutions during the match"""
        substitutions = []
        
        # Simple substitution logic based on fitness and match situation
        if match_minute >= 60:
            # Check for tired players (fitness < 60)
            tired_players = [p for p in current_players if p.fitness < 60]
            
            # Also consider match situation
            home_goals, away_goals = current_score
            goal_difference = home_goals - away_goals
            
            # If losing, prioritize attacking substitutions
            if goal_difference < 0 and match_minute >= 70:
                # Look for fresh attacking players
                attacking_subs = [p for p in available_substitutes 
                                if p.position in [Position.ST, Position.W, Position.AM] 
                                and p.fitness > 80]
                tired_attackers = [p for p in tired_players 
                                 if p.position in [Position.ST, Position.W, Position.AM]]
                
                # Make attacking substitutions
                for i, tired_player in enumerate(tired_attackers[:len(attacking_subs)]):
                    if i < len(attacking_subs):
                        substitutions.append((tired_player, attacking_subs[i]))
                        
            # General fitness-based substitutions
            elif len(tired_players) > 0:
                fresh_subs = [p for p in available_substitutes if p.fitness > 80]
                for i, tired_player in enumerate(tired_players[:len(fresh_subs)]):
                    if i < len(fresh_subs):
                        substitutions.append((tired_player, fresh_subs[i]))
                        
        return substitutions
        
    def post_match_analysis(self, match_result: str, performance_rating: int):
        """Analyze match performance and adjust coach attributes"""
        # Adjust morale based on result
        if match_result == "win":
            self.coach.update_morale(5)
        elif match_result == "loss":
            self.coach.update_morale(-5)
        else:
            self.coach.update_morale(1)
            
        # Adjust tactical knowledge based on performance
        if performance_rating > 80:
            self.coach.tactical_knowledge = min(20, self.coach.tactical_knowledge + 1)
        elif performance_rating < 40:
            self.coach.tactical_knowledge = max(1, self.coach.tactical_knowledge - 1)