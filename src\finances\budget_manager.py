"""
Football Manager - Budget Manager
Manages club finances including budgets, revenues, and expenses.
"""

from dataclasses import dataclass
from typing import List, Dict, Optional
from datetime import datetime, date
import random

from club.club import Club

@dataclass
class FinancialReport:
    period: str  # e.g., "2025-01" for January 2025
    revenue: int
    expenses: int
    net_result: int
    cash_balance: int
    transfer_budget_remaining: int
    wage_budget_remaining: int

class BudgetManager:
    def __init__(self, club: Club):
        self.club = club
        self.monthly_reports: List[FinancialReport] = []
        self.annual_budget = {
            "transfer_budget": club.finances.transfer_budget,
            "wage_budget": club.finances.wage_budget,
            "operating_expenses": 0,  # Will be calculated
            "revenue_target": 0       # Will be calculated
        }
        
    def process_monthly_finances(self, month: int, year: int) -> FinancialReport:
        """Process all monthly financial transactions"""
        # Calculate monthly revenues
        monthly_revenue = self._calculate_monthly_revenue(month, year)
        
        # Calculate monthly expenses
        monthly_expenses = self._calculate_monthly_expenses(month, year)
        
        # Update club finances
        self.club.update_finances(revenue=monthly_revenue, expense=monthly_expenses)
        
        # Create financial report
        report = FinancialReport(
            period=f"{year}-{month:02d}",
            revenue=monthly_revenue,
            expenses=monthly_expenses,
            net_result=monthly_revenue - monthly_expenses,
            cash_balance=self.club.finances.cash_balance,
            transfer_budget_remaining=self._calculate_remaining_transfer_budget(),
            wage_budget_remaining=self._calculate_remaining_wage_budget()
        )
        
        self.monthly_reports.append(report)
        return report
        
    def _calculate_monthly_revenue(self, month: int, year: int) -> int:
        """Calculate monthly revenue from various sources"""
        total_revenue = 0
        
        # Ticket sales (higher for home matches)
        match_days = self._get_match_days_in_month(month, year)
        ticket_revenue = match_days * self._calculate_average_ticket_revenue()
        total_revenue += ticket_revenue
        
        # TV money (fixed monthly amount for top clubs)
        tv_revenue = self._calculate_tv_revenue()
        total_revenue += tv_revenue
        
        # Sponsorship (monthly portion of annual deal)
        sponsorship_revenue = self._calculate_sponsorship_revenue()
        total_revenue += sponsorship_revenue
        
        # Merchandising (variable based on club success)
        merch_revenue = self._calculate_merchandising_revenue()
        total_revenue += merch_revenue
        
        # Prize money (if applicable)
        prize_revenue = self._calculate_prize_money(month)
        total_revenue += prize_revenue
        
        return total_revenue
        
    def _calculate_monthly_expenses(self, month: int, year: int) -> int:
        """Calculate monthly expenses"""
        total_expenses = 0
        
        # Player wages (weekly, so multiply by ~4.33 for monthly)
        wage_bill = self._calculate_current_wage_bill()
        monthly_wages = int(wage_bill * 4.33)
        total_expenses += monthly_wages
        
        # Staff wages (coaching and other staff)
        staff_wages = self._calculate_staff_wages()
        total_expenses += staff_wages
        
        # Facility maintenance
        facility_costs = self._calculate_facility_costs()
        total_expenses += facility_costs
        
        # Youth academy costs
        youth_costs = self._calculate_youth_costs()
        total_expenses += youth_costs
        
        # Other operational costs
        operational_costs = self._calculate_operational_costs()
        total_expenses += operational_costs
        
        return total_expenses
        
    def _get_match_days_in_month(self, month: int, year: int) -> int:
        """Get number of match days in a month (simplified)"""
        # This would normally check the actual fixture schedule
        # For now, we'll use a simple approximation
        if month in [8, 9, 10, 11, 12, 1, 2, 3, 4, 5]:  # Season months
            # Assume 2-4 matches per month during season
            return random.randint(2, 4)
        else:
            # Off-season months
            return random.randint(0, 2)
            
    def _calculate_average_ticket_revenue(self) -> int:
        """Calculate average revenue per match from tickets"""
        # Based on stadium capacity and ticket prices
        capacity = self.club.facilities.stadium_capacity
        average_attendance = int(capacity * 0.8)  # 80% average attendance
        average_ticket_price = 25  # Euro
        return average_attendance * average_ticket_price
        
    def _calculate_tv_revenue(self) -> int:
        """Calculate monthly TV revenue based on club status"""
        # Higher reputation clubs get more TV money
        base_tv = 500000  # Base amount
        reputation_bonus = (self.club.reputation.overall - 10) * 50000
        return max(100000, base_tv + reputation_bonus)  # Minimum 100k
        
    def _calculate_sponsorship_revenue(self) -> int:
        """Calculate monthly sponsorship revenue"""
        # Annual sponsorship deal divided by 12
        # This would normally be more complex with multiple sponsors
        base_sponsorship = self.club.reputation.overall * 200000
        return base_sponsorship // 12
        
    def _calculate_merchandising_revenue(self) -> int:
        """Calculate monthly merchandising revenue"""
        # Based on club success and reputation
        base_merch = self.club.reputation.overall * 50000
        # Add bonus for recent success (simplified)
        success_bonus = 0
        if self.club.league_position <= 4:  # Top 4
            success_bonus = 100000
        elif self.club.league_position <= 8:  # Top 8
            success_bonus = 50000
        return base_merch // 12 + success_bonus
        
    def _calculate_prize_money(self, month: int) -> int:
        """Calculate prize money for competitions"""
        # This would check actual competition progress
        # For now, simplified
        if month == 12:  # December - Champions League/Europa League group stage
            if self.club.reputation.overall >= 18:
                return 2000000  # Champions League
            elif self.club.reputation.overall >= 15:
                return 500000   # Europa League
        elif month == 5:  # May - End of season bonuses
            if self.club.league_position == 1:
                return 5000000  # League winners
            elif self.club.league_position <= 4:
                return 2000000  # Top 4
        return 0
        
    def _calculate_current_wage_bill(self) -> int:
        """Calculate current weekly wage bill"""
        return sum(getattr(player, 'wage', 50000) for player in self.club.players)
        
    def _calculate_staff_wages(self) -> int:
        """Calculate weekly staff wages"""
        # This would include coaching staff, medical, scouts, etc.
        # Simplified calculation
        base_staff_cost = len(self.club.coaching_staff) * 25000
        return base_staff_cost
        
    def _calculate_facility_costs(self) -> int:
        """Calculate weekly facility maintenance costs"""
        # Based on stadium size and condition
        base_cost = self.club.facilities.stadium_capacity // 100
        condition_factor = (11 - self.club.facilities.stadium_condition) / 10
        return int(base_cost * (1 + condition_factor) * 1000)
        
    def _calculate_youth_costs(self) -> int:
        """Calculate weekly youth academy costs"""
        # Based on youth facility quality
        return self.club.facilities.youth_facilities * 5000
        
    def _calculate_operational_costs(self) -> int:
        """Calculate other weekly operational costs"""
        # Travel, utilities, insurance, etc.
        return 200000
        
    def _calculate_remaining_transfer_budget(self) -> int:
        """Calculate remaining transfer budget"""
        # This would track actual transfer spending
        # For now, simplified
        return max(0, self.club.finances.transfer_budget - 10000000)  # Assume 10M spent
        
    def _calculate_remaining_wage_budget(self) -> int:
        """Calculate remaining wage budget"""
        current_wage_bill = self._calculate_current_wage_bill()
        return max(0, self.club.finances.wage_budget - (current_wage_bill * 52))
        
    def approve_transfer_expense(self, amount: int) -> bool:
        """Check if club can afford a transfer expense"""
        return self.club.can_afford_transfer(amount)
        
    def approve_wage_expense(self, weekly_wage: int) -> bool:
        """Check if club can afford a new wage commitment"""
        return self.club.can_afford_wage(weekly_wage)
        
    def calculate_transfer_budget(self) -> int:
        """Calculate available transfer budget"""
        return self._calculate_remaining_transfer_budget()
        
    def get_financial_report(self, period: str = None) -> Optional[FinancialReport]:
        """Get financial report for a specific period or latest"""
        if period:
            for report in self.monthly_reports:
                if report.period == period:
                    return report
        elif self.monthly_reports:
            return self.monthly_reports[-1]
        return None
        
    def get_annual_projection(self) -> Dict[str, int]:
        """Get projected annual financial figures"""
        if not self.monthly_reports:
            return {}
            
        # Simple projection based on average monthly figures
        avg_monthly_revenue = sum(r.revenue for r in self.monthly_reports) // len(self.monthly_reports)
        avg_monthly_expenses = sum(r.expenses for r in self.monthly_reports) // len(self.monthly_reports)
        
        return {
            "projected_annual_revenue": avg_monthly_revenue * 12,
            "projected_annual_expenses": avg_monthly_expenses * 12,
            "projected_annual_profit": (avg_monthly_revenue - avg_monthly_expenses) * 12,
            "current_cash_balance": self.club.finances.cash_balance
        }