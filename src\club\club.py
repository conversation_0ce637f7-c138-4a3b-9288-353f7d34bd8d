"""
Football Manager - Club Class
Base class for football clubs with all core attributes and methods.
"""

from dataclasses import dataclass
from typing import List, Dict, Optional
from datetime import datetime

@dataclass
class ClubFinances:
    transfer_budget: int
    wage_budget: int
    total_revenue: int
    total_expenses: int
    cash_balance: int
    
@dataclass
class ClubFacilities:
    stadium_name: str
    stadium_capacity: int
    stadium_condition: int  # 1-10 scale
    training_facilities: int  # 1-10 scale
    youth_facilities: int  # 1-10 scale
    
@dataclass
class ClubReputation:
    overall: int  # 1-20 scale
    domestic: int
    continental: int
    global_standing: int

class Club:
    def __init__(self, club_data: Dict):
        # Basic information
        self.name = club_data['nome']
        self.city = club_data['citta']
        self.country = club_data.get('country', 'Italia')
        self.founded_year = club_data.get('founded', 1900)
        
        # Facilities
        self.facilities = ClubFacilities(
            stadium_name=club_data['stadio'],
            stadium_capacity=club_data['capienza_stadio'],
            stadium_condition=club_data['condizione_stadio'],
            training_facilities=club_data['strutture_allenamento'],
            youth_facilities=club_data['strutture_giovanili']
        )
        
        # Finances
        self.finances = ClubFinances(
            transfer_budget=club_data['budget_trasferimenti_eur'],
            wage_budget=club_data['budget_stipendi_eur'],
            total_revenue=0,
            total_expenses=0,
            cash_balance=club_data['budget_trasferimenti_eur']
        )
        
        # Reputation and standing
        self.reputation = ClubReputation(
            overall=club_data['reputazione'],
            domestic=club_data['reputazione'],
            continental=max(1, club_data['reputazione'] - 3),
            global_standing=max(1, club_data['reputazione'] - 5)
        )
        
        # Tactical setup
        self.default_formation = club_data['formazione_predefinita']
        self.youth_recruitment = club_data['reclutamento_giovanile']
        
        # Historical data
        self.last_season_position = club_data.get('ultima_posizione_campionato_precedente', 10)
        self.trophies = {}
        self.history = []
        
        # Current season data
        self.current_league = None
        self.league_position = 0
        self.points = 0
        self.matches_played = 0
        self.wins = 0
        self.draws = 0
        self.losses = 0
        self.goals_for = 0
        self.goals_against = 0
        
        # Squads and staff
        self.players = []
        self.coaching_staff = []
        self.board_members = []
        
        # Current season objectives
        self.season_objectives = []
        self.board_satisfaction = 50  # 0-100 scale
        
    def update_finances(self, revenue: int = 0, expense: int = 0):
        """Update club finances"""
        self.finances.total_revenue += revenue
        self.finances.total_expenses += expense
        self.finances.cash_balance += revenue - expense
        
    def can_afford_transfer(self, amount: int) -> bool:
        """Check if club can afford a transfer"""
        return self.finances.cash_balance >= amount
        
    def can_afford_wage(self, weekly_wage: int) -> bool:
        """Check if club can afford new wage"""
        current_wages = sum(player.wage for player in self.players)
        return (current_wages + weekly_wage) * 52 <= self.finances.wage_budget
        
    def add_trophy(self, competition: str, year: int):
        """Add trophy to club history"""
        if competition not in self.trophies:
            self.trophies[competition] = []
        self.trophies[competition].append(year)
        
    def get_trophy_count(self, competition: str = None) -> int:
        """Get trophy count for specific competition or total"""
        if competition:
            return len(self.trophies.get(competition, []))
        return sum(len(trophies) for trophies in self.trophies.values())
        
    def update_league_stats(self, goals_for: int, goals_against: int, result: str):
        """Update league statistics after a match"""
        self.matches_played += 1
        self.goals_for += goals_for
        self.goals_against += goals_against
        
        if result == 'win':
            self.wins += 1
            self.points += 3
        elif result == 'draw':
            self.draws += 1
            self.points += 1
        else:
            self.losses += 1
            
    def get_goal_difference(self) -> int:
        """Get goal difference"""
        return self.goals_for - self.goals_against
        
    def calculate_form(self, last_matches: int = 5) -> str:
        """Calculate recent form (last 5 matches)"""
        # This would be implemented with match history
        return "WWDLW"  # Placeholder
        
    def get_club_value(self) -> int:
        """Calculate estimated club value"""
        base_value = self.reputation.overall * 10000000
        facility_bonus = (self.facilities.stadium_capacity / 1000) * 50000
        player_value = sum(getattr(player, 'market_value', 0) for player in self.players)
        
        return int(base_value + facility_bonus + player_value * 0.8)
        
    def __str__(self) -> str:
        return f"{self.name} ({self.city})"
        
    def __repr__(self) -> str:
        return f"Club(name='{self.name}', reputation={self.reputation.overall})"