"""
Football Manager - Player Class
Base class for football players with attributes, position, and career data.
"""

from dataclasses import dataclass
from typing import List, Optional, Dict
from datetime import datetime, date
from enum import Enum

class Position(Enum):
    GK = "Goalkeeper"
    SW = "Sweeper"
    CB = "Centre Back"
    LB = "Left Back"
    RB = "Right Back"
    LWB = "Left Wing Back"
    RWB = "Right Wing Back"
    DM = "Defensive Midfielder"
    CM = "Central Midfielder"
    LM = "Left Midfielder"
    RM = "Right Midfielder"
    AM = "Attacking Midfielder"
    LW = "Left Winger"
    RW = "Right Winger"
    CF = "Centre Forward"
    LF = "Left Forward"
    RF = "Right Forward"
    ST = "Striker"

class Foot(Enum):
    LEFT = "left"
    RIGHT = "right"
    BOTH = "both"

@dataclass
class PlayerAttributes:
    # Technical attributes (1-20 scale)
    crossing: int
    dribbling: int
    finishing: int
    first_touch: int
    free_kick_taking: int
    heading: int
    long_shots: int
    long_throws: int
    marking: int
    passing: int
    penalty_taking: int
    tackling: int
    technique: int
    
    # Mental attributes (1-20 scale)
    aggression: int
    anticipation: int
    bravery: int
    composure: int
    concentration: int
    decisions: int
    determination: int
    flair: int
    leadership: int
    off_the_ball: int
    positioning: int
    teamwork: int
    vision: int
    work_rate: int
    
    # Physical attributes (1-20 scale)
    acceleration: int
    agility: int
    balance: int
    jumping_reach: int
    natural_fitness: int
    pace: int
    stamina: int
    strength: int

@dataclass
class PlayerContract:
    start_date: date
    end_date: date
    wage: int  # Weekly wage
    bonuses: Dict[str, int]
    release_clause: Optional[int]

class Player:
    def __init__(self, first_name: str, last_name: str, nationality: str, birth_date: date):
        # Basic information
        self.first_name = first_name
        self.last_name = last_name
        self.full_name = f"{first_name} {last_name}"
        self.nationality = nationality
        self.birth_date = birth_date
        self.age = self._calculate_age()
        
        # Physical characteristics
        self.height = 175  # cm
        self.weight = 75   # kg
        self.preferred_foot = Foot.RIGHT
        
        # Position and role
        self.position = Position.CM
        self.secondary_positions = []
        
        # Attributes (initialized with default values)
        self.attributes = self._initialize_attributes()
        
        # Career data
        self.current_club = None
        self.contract = None
        self.market_value = 0
        self.squad_number = 0
        
        # Performance data
        self.current_ability = 0  # Overall rating 1-200
        self.potential_ability = 0  # Potential rating 1-200
        self.form = 50  # Current form 1-100
        self.morale = 50  # Morale 1-100
        self.fitness = 100  # Fitness 1-100
        self.injury_proneness = 10  # 1-20 scale
        
        # Season statistics
        self.goals = 0
        self.assists = 0
        self.appearances = 0
        self.minutes_played = 0
        self.yellow_cards = 0
        self.red_cards = 0
        
        # Career history
        self.career_history = []
        self.international_caps = 0
        self.international_goals = 0
        
    def _calculate_age(self) -> int:
        """Calculate current age"""
        today = date.today()
        return today.year - self.birth_date.year - \
               ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
    
    def _initialize_attributes(self) -> PlayerAttributes:
        """Initialize player attributes with default values"""
        return PlayerAttributes(
            # Technical
            crossing=10, dribbling=10, finishing=10, first_touch=10,
            free_kick_taking=10, heading=10, long_shots=10, long_throws=10,
            marking=10, passing=10, penalty_taking=10, tackling=10, technique=10,
            # Mental
            aggression=10, anticipation=10, bravery=10, composure=10,
            concentration=10, decisions=10, determination=10, flair=10,
            leadership=10, off_the_ball=10, positioning=10, teamwork=10,
            vision=10, work_rate=10,
            # Physical
            acceleration=10, agility=10, balance=10, jumping_reach=10,
            natural_fitness=10, pace=10, stamina=10, strength=10
        )
    
    def calculate_overall_rating(self) -> int:
        """Calculate overall rating based on position and attributes"""
        if self.position == Position.GK:
            return self._calculate_goalkeeper_rating()
        elif self.position in [Position.CB, Position.SW]:
            return self._calculate_defender_rating()
        elif self.position in [Position.DM, Position.CM, Position.AM]:
            return self._calculate_midfielder_rating()
        else:
            return self._calculate_forward_rating()
    
    def _calculate_goalkeeper_rating(self) -> int:
        """Calculate rating for goalkeeper"""
        key_attrs = [
            self.attributes.positioning, self.attributes.anticipation,
            self.attributes.bravery, self.attributes.concentration,
            self.attributes.decisions, self.attributes.agility
        ]
        return int(sum(key_attrs) / len(key_attrs))
    
    def _calculate_defender_rating(self) -> int:
        """Calculate rating for defender"""
        key_attrs = [
            self.attributes.tackling, self.attributes.marking,
            self.attributes.positioning, self.attributes.heading,
            self.attributes.strength, self.attributes.concentration,
            self.attributes.bravery, self.attributes.anticipation
        ]
        return int(sum(key_attrs) / len(key_attrs))
    
    def _calculate_midfielder_rating(self) -> int:
        """Calculate rating for midfielder"""
        key_attrs = [
            self.attributes.passing, self.attributes.technique,
            self.attributes.first_touch, self.attributes.vision,
            self.attributes.decisions, self.attributes.teamwork,
            self.attributes.stamina, self.attributes.work_rate
        ]
        return int(sum(key_attrs) / len(key_attrs))
    
    def _calculate_forward_rating(self) -> int:
        """Calculate rating for forward"""
        key_attrs = [
            self.attributes.finishing, self.attributes.off_the_ball,
            self.attributes.composure, self.attributes.acceleration,
            self.attributes.pace, self.attributes.dribbling,
            self.attributes.first_touch, self.attributes.anticipation
        ]
        return int(sum(key_attrs) / len(key_attrs))
    
    def update_season_stats(self, goals: int = 0, assists: int = 0, 
                          minutes: int = 0, cards: Dict[str, int] = None):
        """Update season statistics"""
        self.goals += goals
        self.assists += assists
        self.minutes_played += minutes
        
        if minutes > 0:
            self.appearances += 1
            
        if cards:
            self.yellow_cards += cards.get('yellow', 0)
            self.red_cards += cards.get('red', 0)
    
    def get_market_value(self) -> int:
        """Calculate estimated market value"""
        base_value = self.current_ability * 50000
        age_modifier = max(0.3, 2.0 - (abs(26 - self.age) * 0.1))
        potential_bonus = max(0, self.potential_ability - self.current_ability) * 10000
        
        return int((base_value + potential_bonus) * age_modifier)
    
    def can_play_position(self, position: Position) -> bool:
        """Check if player can play in specific position"""
        return position == self.position or position in self.secondary_positions
    
    def __str__(self) -> str:
        return f"{self.full_name} ({self.age}, {self.position.value})"
    
    def __repr__(self) -> str:
        return f"Player(name='{self.full_name}', position='{self.position.value}', age={self.age})"