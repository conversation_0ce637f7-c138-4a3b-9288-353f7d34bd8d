"""
Menu Consiglio di Amministrazione
Gestisce rapporti con la dirigenza e decisioni strategiche
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List
from .base_menu import BaseMenu
from ..gui_config import COLORS, FONTS, ICONS


class BoardMenu(BaseMenu):
    """Menu per la gestione del consiglio di amministrazione"""
    
    def setup_ui(self):
        """Configura l'interfaccia del menu consiglio"""
        # Header
        self.create_section_header(self.main_frame, "Consiglio di Amministrazione", 
                                 "Rapporti con la dirigenza e decisioni strategiche")
        
        # Crea le schede per diverse sezioni del consiglio
        tabs_config = [
            {'text': 'Membri CdA', 'setup_func': self._setup_members_tab},
            {'text': 'Obiettivi', 'setup_func': self._setup_objectives_tab},
            {'text': 'Budget', 'setup_func': self._setup_budget_tab},
            {'text': 'Riunioni', 'setup_func': self._setup_meetings_tab}
        ]
        
        self.notebook = self.create_tabs(self.main_frame, tabs_config)
    
    def _setup_members_tab(self, parent: tk.Widget):
        """Configura la scheda membri del consiglio"""
        # Membri del consiglio
        members_frame = ttk.LabelFrame(parent, text="Membri del Consiglio", padding=15)
        members_frame.pack(fill='x', pady=(0, 10))
        
        members_columns = [
            {'id': 'name', 'text': 'Nome', 'width': 120},
            {'id': 'role', 'text': 'Ruolo', 'width': 120},
            {'id': 'influence', 'text': 'Influenza', 'width': 80},
            {'id': 'relationship', 'text': 'Rapporto', 'width': 80},
            {'id': 'satisfaction', 'text': 'Soddisfazione', 'width': 80},
            {'id': 'priority', 'text': 'Priorità', 'width': 100}
        ]
        
        # Genera membri del consiglio basati sui dati del club
        president_name = self.club_data.get('presidente', 'Aurelio De Laurentiis')
        
        members_data = [
            [president_name, "Presidente", "Molto Alta", "Buono", "78%", "Risultati Sportivi"],
            ["Maria Rossi", "Vice Presidente", "Alta", "Ottimo", "85%", "Stabilità Finanziaria"],
            ["Giuseppe Bianchi", "Amministratore Delegato", "Alta", "Buono", "72%", "Crescita Ricavi"],
            ["Anna Verdi", "Direttore Finanziario", "Media", "Neutro", "68%", "Controllo Costi"],
            ["Marco Neri", "Consigliere", "Media", "Buono", "80%", "Sviluppo Giovanile"],
            ["Luca Blu", "Consigliere", "Bassa", "Buono", "75%", "Marketing"]
        ]
        
        members_tree = self.create_data_table(members_frame, members_columns, members_data, height=6)
        
        # Pulsanti membri
        members_buttons = [
            {'text': '🤝 Migliora Rapporto', 'command': self._improve_board_relationship, 'style': 'Primary.TButton'},
            {'text': '💬 Colloquio Privato', 'command': self._private_meeting, 'style': 'Secondary.TButton'},
            {'text': '📊 Analisi Influenza', 'command': self._analyze_board_influence, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(members_frame, members_buttons)
        
        # Fiducia del consiglio
        trust_frame = ttk.LabelFrame(parent, text="Fiducia del Consiglio", padding=15)
        trust_frame.pack(fill='x', pady=10)
        
        # Grid per fiducia
        trust_grid = ttk.Frame(trust_frame)
        trust_grid.pack(fill='x')
        
        # Colonna sinistra - Metriche attuali
        left_trust = ttk.Frame(trust_grid)
        left_trust.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_trust, text="📊 Fiducia Attuale", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        trust_data = [
            ("Fiducia Generale", "76%", COLORS['success']),
            ("Supporto Decisioni", "82%", COLORS['success']),
            ("Soddisfazione Risultati", "71%", COLORS['info']),
            ("Approvazione Gestione", "78%", COLORS['success']),
            ("Consenso Strategia", "85%", COLORS['success']),
            ("Stabilità Posizione", "Alta", COLORS['success'])
        ]
        
        for label, value, color in trust_data:
            self.create_info_row(left_trust, label, value, color)
        
        # Colonna destra - Trend
        right_trust = ttk.Frame(trust_grid)
        right_trust.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_trust, text="📈 Trend (3 mesi)", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        trend_data = [
            ("Variazione Fiducia", "+8%"),
            ("Pressione", "Bassa"),
            ("Aspettative", "Moderate"),
            ("Pazienza", "Alta"),
            ("Supporto Budget", "Forte"),
            ("Rinnovo Contratto", "Probabile")
        ]
        
        for label, value in trend_data:
            color = COLORS['success'] if any(x in str(value) for x in ['+', 'Alta', 'Forte', 'Probabile']) else None
            self.create_info_row(right_trust, label, value, color)
        
        # Fattori di rischio
        risks_frame = ttk.LabelFrame(parent, text="Fattori di Rischio", padding=15)
        risks_frame.pack(fill='both', expand=True)
        
        risks_columns = [
            {'id': 'risk', 'text': 'Fattore di Rischio', 'width': 180},
            {'id': 'probability', 'text': 'Probabilità', 'width': 80},
            {'id': 'impact', 'text': 'Impatto', 'width': 80},
            {'id': 'mitigation', 'text': 'Mitigazione', 'width': 150},
            {'id': 'status', 'text': 'Stato', 'width': 80}
        ]
        
        risks_data = [
            ["Mancata Qualificazione Europa", "Bassa", "Alto", "Migliorare risultati", "Monitorato"],
            ["Perdite Finanziarie Eccessive", "Media", "Molto Alto", "Controllo spese", "Attivo"],
            ["Conflitti con Tifosi", "Bassa", "Medio", "Comunicazione", "Sotto controllo"],
            ["Scandali Mediatici", "Molto Bassa", "Alto", "Comportamento esemplare", "Prevenuto"],
            ["Risultati Deludenti Derby", "Media", "Medio", "Preparazione speciale", "Pianificato"]
        ]
        
        self.create_data_table(risks_frame, risks_columns, risks_data, height=5)
        
        # Pulsanti rischi
        risks_buttons = [
            {'text': '⚠️ Piano Mitigazione', 'command': self._mitigation_plan, 'style': 'Primary.TButton'},
            {'text': '📋 Report Rischi', 'command': self._risk_report, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(risks_frame, risks_buttons)
    
    def _setup_objectives_tab(self, parent: tk.Widget):
        """Configura la scheda obiettivi"""
        # Obiettivi stagionali
        seasonal_frame = ttk.LabelFrame(parent, text="Obiettivi Stagionali", padding=15)
        seasonal_frame.pack(fill='x', pady=(0, 10))
        
        seasonal_columns = [
            {'id': 'objective', 'text': 'Obiettivo', 'width': 200},
            {'id': 'priority', 'text': 'Priorità', 'width': 80},
            {'id': 'progress', 'text': 'Progresso', 'width': 80},
            {'id': 'deadline', 'text': 'Scadenza', 'width': 80},
            {'id': 'reward', 'text': 'Bonus', 'width': 80},
            {'id': 'penalty', 'text': 'Penale', 'width': 80}
        ]
        
        seasonal_data = [
            ["Qualificazione Champions League", "Molto Alta", "65%", "Fine Stagione", "€2M", "€500K"],
            ["Vittoria Coppa Italia", "Alta", "40%", "Maggio 2025", "€1M", "€200K"],
            ["Pareggio Bilancio", "Alta", "78%", "Giugno 2025", "€500K", "€300K"],
            ["Sviluppo 3 Giovani", "Media", "55%", "Fine Stagione", "€300K", "€100K"],
            ["Aumento Ricavi 10%", "Media", "82%", "Giugno 2025", "€200K", "€50K"]
        ]
        
        seasonal_tree = self.create_data_table(seasonal_frame, seasonal_columns, seasonal_data, height=5)
        
        # Pulsanti obiettivi stagionali
        seasonal_buttons = [
            {'text': '🎯 Dettagli Obiettivo', 'command': self._objective_details, 'style': 'Primary.TButton'},
            {'text': '📈 Aggiorna Progresso', 'command': self._update_progress, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(seasonal_frame, seasonal_buttons)
        
        # Obiettivi a lungo termine
        longterm_frame = ttk.LabelFrame(parent, text="Obiettivi a Lungo Termine", padding=15)
        longterm_frame.pack(fill='x', pady=10)
        
        longterm_columns = [
            {'id': 'objective', 'text': 'Obiettivo', 'width': 200},
            {'id': 'timeframe', 'text': 'Tempistica', 'width': 100},
            {'id': 'progress', 'text': 'Progresso', 'width': 80},
            {'id': 'investment', 'text': 'Investimento', 'width': 100},
            {'id': 'roi', 'text': 'ROI Atteso', 'width': 80}
        ]
        
        longterm_data = [
            ["Nuovo Stadio", "5 anni", "15%", "€200M", "+150%"],
            ["Centro Sportivo Elite", "3 anni", "35%", "€50M", "+80%"],
            ["Brand Internazionale", "4 anni", "45%", "€30M", "+200%"],
            ["Autosufficienza Finanziaria", "2 anni", "70%", "€20M", "+120%"],
            ["Top 10 Europa", "5 anni", "25%", "€100M", "+300%"]
        ]
        
        self.create_data_table(longterm_frame, longterm_columns, longterm_data, height=5)
        
        # Performance vs obiettivi
        performance_frame = ttk.LabelFrame(parent, text="Performance vs Obiettivi", padding=15)
        performance_frame.pack(fill='both', expand=True)
        
        # Grid per performance
        performance_grid = ttk.Frame(performance_frame)
        performance_grid.pack(fill='x')
        
        # Colonna sinistra - Risultati
        left_performance = ttk.Frame(performance_grid)
        left_performance.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_performance, text="🏆 Risultati Attuali", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        results_data = [
            ("Posizione Campionato", "3° posto"),
            ("Punti Conquistati", "41/60 possibili"),
            ("Coppa Italia", "Quarti di finale"),
            ("Champions League", "Ottavi di finale"),
            ("Bilancio", "+€2.3M"),
            ("Giovani Promossi", "2/3")
        ]
        
        for label, value in results_data:
            self.create_info_row(left_performance, label, value)
        
        # Colonna destra - Valutazione
        right_performance = ttk.Frame(performance_grid)
        right_performance.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_performance, text="📊 Valutazione CdA", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        evaluation_data = [
            ("Soddisfazione Generale", "76%"),
            ("Obiettivi Raggiunti", "3/5"),
            ("Performance Rating", "B+"),
            ("Fiducia Futura", "Alta"),
            ("Supporto Progetti", "Forte"),
            ("Rinnovo Raccomandato", "Sì")
        ]
        
        for label, value in evaluation_data:
            color = COLORS['success'] if any(x in str(value) for x in ['Alta', 'Forte', 'Sì', 'B+']) else None
            self.create_info_row(right_performance, label, value, color)
    
    def _setup_budget_tab(self, parent: tk.Widget):
        """Configura la scheda budget"""
        # Budget approvato
        approved_frame = ttk.LabelFrame(parent, text="Budget Approvato", padding=15)
        approved_frame.pack(fill='x', pady=(0, 10))
        
        approved_columns = [
            {'id': 'category', 'text': 'Categoria', 'width': 150},
            {'id': 'approved', 'text': 'Approvato', 'width': 100},
            {'id': 'spent', 'text': 'Speso', 'width': 100},
            {'id': 'remaining', 'text': 'Rimanente', 'width': 100},
            {'id': 'percentage', 'text': '% Utilizzo', 'width': 80},
            {'id': 'status', 'text': 'Stato', 'width': 80}
        ]
        
        approved_data = [
            ["Stipendi Giocatori", "€45M", "€32M", "€13M", "71%", "OK"],
            ["Trasferimenti", "€25M", "€18M", "€7M", "72%", "OK"],
            ["Staff Tecnico", "€8M", "€6.2M", "€1.8M", "78%", "OK"],
            ["Strutture", "€15M", "€2.3M", "€12.7M", "15%", "Sottoutilizzato"],
            ["Marketing", "€5M", "€3.8M", "€1.2M", "76%", "OK"],
            ["Operazioni", "€12M", "€9.1M", "€2.9M", "76%", "OK"]
        ]
        
        approved_tree = self.create_data_table(approved_frame, approved_columns, approved_data, height=6)
        
        # Pulsanti budget
        budget_buttons = [
            {'text': '💰 Richiedi Fondi Extra', 'command': self._request_extra_funds, 'style': 'Primary.TButton'},
            {'text': '🔄 Rialloca Budget', 'command': self._reallocate_budget, 'style': 'Secondary.TButton'},
            {'text': '📊 Report Finanziario', 'command': self._financial_report, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(approved_frame, budget_buttons)
        
        # Richieste di budget
        requests_frame = ttk.LabelFrame(parent, text="Richieste di Budget", padding=15)
        requests_frame.pack(fill='x', pady=10)
        
        requests_columns = [
            {'id': 'request', 'text': 'Richiesta', 'width': 180},
            {'id': 'amount', 'text': 'Importo', 'width': 100},
            {'id': 'justification', 'text': 'Giustificazione', 'width': 150},
            {'id': 'urgency', 'text': 'Urgenza', 'width': 80},
            {'id': 'status', 'text': 'Stato', 'width': 100}
        ]
        
        requests_data = [
            ["Rinnovo Contratto Rossi", "€3M", "Evitare perdita a zero", "Alta", "In valutazione"],
            ["Nuovo Centrocampista", "€15M", "Rinforzo necessario", "Media", "Approvata"],
            ["Ristrutturazione Stadio", "€2M", "Sicurezza e comfort", "Bassa", "Rimandata"],
            ["Bonus Staff Vittorie", "€500K", "Incentivo performance", "Media", "Approvata"]
        ]
        
        self.create_data_table(requests_frame, requests_columns, requests_data, height=4)
        
        # Proiezioni finanziarie
        projections_frame = ttk.LabelFrame(parent, text="Proiezioni Finanziarie", padding=15)
        projections_frame.pack(fill='both', expand=True)
        
        # Grid per proiezioni
        projections_grid = ttk.Frame(projections_frame)
        projections_grid.pack(fill='x')
        
        # Colonna sinistra - Ricavi previsti
        left_projections = ttk.Frame(projections_grid)
        left_projections.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        ttk.Label(left_projections, text="📈 Ricavi Previsti", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        revenue_projections = [
            ("Biglietteria", "€8.5M"),
            ("Sponsorizzazioni", "€25M"),
            ("Diritti TV", "€35M"),
            ("Merchandising", "€4M"),
            ("Premi UEFA", "€12M"),
            ("Totale Ricavi", "€84.5M")
        ]
        
        for label, value in revenue_projections:
            color = COLORS['success'] if 'Totale' in label else None
            self.create_info_row(left_projections, label, value, color)
        
        # Colonna destra - Costi previsti
        right_projections = ttk.Frame(projections_grid)
        right_projections.pack(side='right', fill='both', expand=True)
        
        ttk.Label(right_projections, text="📉 Costi Previsti", font=FONTS['subtitle']).pack(anchor='w', pady=(0, 10))
        
        cost_projections = [
            ("Stipendi Totali", "€53M"),
            ("Ammortamenti", "€15M"),
            ("Operazioni", "€12M"),
            ("Tasse e Imposte", "€3M"),
            ("Altri Costi", "€2M"),
            ("Totale Costi", "€85M")
        ]
        
        for label, value in cost_projections:
            color = COLORS['warning'] if 'Totale' in label else None
            self.create_info_row(right_projections, label, value, color)
        
        # Risultato previsto
        result_frame = ttk.Frame(projections_frame)
        result_frame.pack(fill='x', pady=(10, 0))
        
        ttk.Label(result_frame, text="💰 Risultato Previsto: -€500K", 
                 font=FONTS['subtitle'], foreground=COLORS['warning']).pack(anchor='center')
    
    def _setup_meetings_tab(self, parent: tk.Widget):
        """Configura la scheda riunioni"""
        # Prossime riunioni
        upcoming_meetings_frame = ttk.LabelFrame(parent, text="Prossime Riunioni", padding=15)
        upcoming_meetings_frame.pack(fill='x', pady=(0, 10))
        
        upcoming_columns = [
            {'id': 'date', 'text': 'Data', 'width': 100},
            {'id': 'time', 'text': 'Ora', 'width': 60},
            {'id': 'type', 'text': 'Tipo', 'width': 120},
            {'id': 'agenda', 'text': 'Ordine del Giorno', 'width': 200},
            {'id': 'participants', 'text': 'Partecipanti', 'width': 80},
            {'id': 'location', 'text': 'Luogo', 'width': 100}
        ]
        
        upcoming_data = [
            ["25/01/2025", "10:00", "CdA Ordinario", "Budget Q1, Mercato Gennaio", "6", "Sala Riunioni"],
            ["30/01/2025", "15:00", "Comitato Esecutivo", "Rinnovi Contratti", "3", "Ufficio Presidente"],
            ["05/02/2025", "11:00", "Revisione Strategica", "Obiettivi Stagionali", "8", "Centro Sportivo"],
            ["10/02/2025", "14:00", "Budget Committee", "Approvazione Spese", "4", "Sala Riunioni"]
        ]
        
        upcoming_tree = self.create_data_table(upcoming_meetings_frame, upcoming_columns, upcoming_data, height=4)
        
        # Pulsanti riunioni
        meetings_buttons = [
            {'text': '📅 Programma Riunione', 'command': self._schedule_meeting, 'style': 'Primary.TButton'},
            {'text': '📋 Prepara Agenda', 'command': self._prepare_agenda, 'style': 'Secondary.TButton'},
            {'text': '📊 Presenta Report', 'command': self._present_report, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(upcoming_meetings_frame, meetings_buttons)
        
        # Storico riunioni
        history_frame = ttk.LabelFrame(parent, text="Storico Riunioni", padding=15)
        history_frame.pack(fill='x', pady=10)
        
        history_columns = [
            {'id': 'date', 'text': 'Data', 'width': 80},
            {'id': 'type', 'text': 'Tipo', 'width': 120},
            {'id': 'duration', 'text': 'Durata', 'width': 60},
            {'id': 'decisions', 'text': 'Decisioni Prese', 'width': 50},
            {'id': 'satisfaction', 'text': 'Soddisfazione', 'width': 80},
            {'id': 'follow_up', 'text': 'Follow-up', 'width': 80}
        ]
        
        history_data = [
            ["15/01", "CdA Straordinario", "2h", "3", "Alta", "Completato"],
            ["20/12", "CdA Ordinario", "1.5h", "5", "Media", "In corso"],
            ["05/12", "Budget Review", "3h", "8", "Alta", "Completato"],
            ["15/11", "Strategia Mercato", "2h", "4", "Media", "Completato"],
            ["01/11", "CdA Ordinario", "1h", "2", "Bassa", "Parziale"]
        ]
        
        self.create_data_table(history_frame, history_columns, history_data, height=5)
        
        # Decisioni in sospeso
        pending_frame = ttk.LabelFrame(parent, text="Decisioni in Sospeso", padding=15)
        pending_frame.pack(fill='both', expand=True)
        
        pending_columns = [
            {'id': 'decision', 'text': 'Decisione', 'width': 200},
            {'id': 'proposed_by', 'text': 'Proposta da', 'width': 100},
            {'id': 'deadline', 'text': 'Scadenza', 'width': 80},
            {'id': 'votes_for', 'text': 'Favorevoli', 'width': 70},
            {'id': 'votes_against', 'text': 'Contrari', 'width': 70},
            {'id': 'status', 'text': 'Stato', 'width': 100}
        ]
        
        pending_data = [
            ["Aumento Budget Trasferimenti", "Allenatore", "31/01", "4", "2", "In discussione"],
            ["Nuovo Sponsor Maglia", "Marketing", "15/02", "5", "1", "Quasi approvata"],
            ["Ristrutturazione Settore Giovanile", "DS", "28/02", "3", "3", "Stallo"],
            ["Rinnovo Contratto Capitano", "Presidente", "20/01", "6", "0", "Approvata"]
        ]
        
        pending_tree = self.create_data_table(pending_frame, pending_columns, pending_data, height=4)
        
        # Pulsanti decisioni
        decisions_buttons = [
            {'text': '✅ Supporta Decisione', 'command': self._support_decision, 'style': 'Primary.TButton'},
            {'text': '❌ Opponi Decisione', 'command': self._oppose_decision, 'style': 'Secondary.TButton'},
            {'text': '🗳️ Chiama Votazione', 'command': self._call_vote, 'style': 'Secondary.TButton'}
        ]
        
        self.create_button_group(pending_frame, decisions_buttons)
    
    # Metodi placeholder per le azioni
    def _improve_board_relationship(self):
        self.show_message("Migliora Rapporto", "Funzionalità in sviluppo", "info")
    
    def _private_meeting(self):
        self.show_message("Colloquio Privato", "Funzionalità in sviluppo", "info")
    
    def _analyze_board_influence(self):
        self.show_message("Analisi Influenza", "Funzionalità in sviluppo", "info")
    
    def _mitigation_plan(self):
        self.show_message("Piano Mitigazione", "Funzionalità in sviluppo", "info")
    
    def _risk_report(self):
        self.show_message("Report Rischi", "Funzionalità in sviluppo", "info")
    
    def _objective_details(self):
        self.show_message("Dettagli Obiettivo", "Funzionalità in sviluppo", "info")
    
    def _update_progress(self):
        self.show_message("Aggiorna Progresso", "Funzionalità in sviluppo", "info")
    
    def _request_extra_funds(self):
        self.show_message("Richiedi Fondi Extra", "Funzionalità in sviluppo", "info")
    
    def _reallocate_budget(self):
        self.show_message("Rialloca Budget", "Funzionalità in sviluppo", "info")
    
    def _financial_report(self):
        self.show_message("Report Finanziario", "Funzionalità in sviluppo", "info")
    
    def _schedule_meeting(self):
        self.show_message("Programma Riunione", "Funzionalità in sviluppo", "info")
    
    def _prepare_agenda(self):
        self.show_message("Prepara Agenda", "Funzionalità in sviluppo", "info")
    
    def _present_report(self):
        self.show_message("Presenta Report", "Funzionalità in sviluppo", "info")
    
    def _support_decision(self):
        self.show_message("Supporta Decisione", "Funzionalità in sviluppo", "info")
    
    def _oppose_decision(self):
        self.show_message("Opponi Decisione", "Funzionalità in sviluppo", "info")
    
    def _call_vote(self):
        self.show_message("Chiama Votazione", "Funzionalità in sviluppo", "info")
